import type { Common } from "@commune/api";
import type { PageLoad } from "./$types";

import { handleUnauthorized } from "$lib";

export interface CommuneJoinRequest {
  id: string;
  communeId: string;
  userId: string;
  status: "pending" | "accepted" | "rejected";
  createdAt: string;
  updatedAt: string;
}

export interface CommuneJoinRequestWithDetails extends CommuneJoinRequest {
  commune: {
    id: string;
    name: Common.Localizations;
    description: Common.Localizations;
    memberCount: number;
    headMember: {
      actorType: "user" | "commune";
      actorId: string;
      name: Common.Localizations;
    };
    images: Common.Images;
    createdAt: string;
    updatedAt: string;
  };
}

export const load: PageLoad = async ({ fetch, url }) => {
  const response = await fetch("/api/commune/join-request?page=1&size=20");

  handleUnauthorized(response, url);

  const joinRequests: CommuneJoinRequest[] = response.ok ? await response.json() : [];

  // Fetch commune details for each join request
  const joinRequestsWithDetails: (CommuneJoinRequestWithDetails | null)[] = await Promise.all(
    joinRequests.map(async (joinRequest) => {
      try {
        const communeResponse = await fetch(`/api/commune/${joinRequest.communeId}`);
        if (communeResponse.ok) {
          const commune = await communeResponse.json();
          return {
            ...joinRequest,
            commune,
          } as CommuneJoinRequestWithDetails;
        }
        // If commune fetch fails, we'll handle this in the component
        console.warn(`Failed to fetch commune ${joinRequest.communeId}: ${communeResponse.statusText}`);
        return null;
      } catch (error) {
        console.error(`Failed to fetch commune ${joinRequest.communeId}:`, error);
        return null;
      }
    })
  );

  // Filter out failed fetches
  const validJoinRequests = joinRequestsWithDetails.filter(
    (joinRequest): joinRequest is CommuneJoinRequestWithDetails => joinRequest !== null
  );

  return {
    joinRequests: validJoinRequests,
    isHasMoreJoinRequests: joinRequests.length === 20, // If we got a full page, there might be more
  };
};
