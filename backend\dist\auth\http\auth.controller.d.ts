import { UserService } from "src/user/user.service";
import { CurrentUser } from "../types";
import { AuthService } from "../auth.service";
import { Request, Response } from "express";
import { Auth } from "@commune/api";
export declare class AuthController {
    private readonly authService;
    private readonly userService;
    constructor(authService: AuthService, userService: UserService);
    test(): Promise<boolean>;
    me(currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
        role: "admin" | "moderator" | "user";
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
    }>;
    otp(body: Auth.SendOtpRequest, ipAddress: string, userAgent: string): Promise<{
        isSent: boolean;
    }>;
    register(req: Request, body: Auth.RegisterRequest, ipAddress: string, userAgent: string): Promise<{
        id: string;
        email: string;
        role: "admin" | "moderator" | "user";
    }>;
    login(req: Request, body: Auth.LoginRequest, ipAddress: string, userAgent: string): Promise<{
        id: string;
        email: string;
        role: "admin" | "moderator" | "user";
    }>;
    signOut(req: Request, res: Response): Promise<void>;
}
