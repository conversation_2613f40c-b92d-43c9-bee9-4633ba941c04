<script lang="ts">
  import type { Common } from "@commune/api";

  import { fetchWithAuth } from "$lib";
  import { Modal } from "$lib/components";
  import { LocalizedInput, LocalizedTextarea } from "../components";

  interface Props {
    locale: Common.LocalizationLocale;
    show: boolean;
    onHide: () => void;
    userData: {
      id: string;
      name: Common.Localizations;
      description: Common.Localizations;
    } | null;
    onProfileUpdated: () => void;
  }

  const i18n = {
    en: {
      editProfile: "Edit Profile",
      name: {
        label: "Name",
        placeholder: "Enter your name",
      },
      description: {
        label: "Description (optional)",
        placeholder: "Tell us about yourself",
      },
      saveChanges: "Save Changes",
      cancel: "Cancel",
      saving: "Saving...",
      nameRequired: "Name is required",
      failedToUpdateProfile: "Failed to update profile",
      profileUpdatedSuccessfully: "Profile updated successfully",
      errorOccurred: "An error occurred while updating profile",
    },

    ru: {
      editProfile: "Редактировать профиль",
      name: {
        label: "Имя",
        placeholder: "Введите ваше имя",
      },
      description: {
        label: "Описание (необязательно)",
        placeholder: "Расскажите о себе",
      },
      saveChanges: "Сохранить изменения",
      cancel: "Отменить",
      saving: "Сохранение...",
      nameRequired: "Имя обязательно",
      failedToUpdateProfile: "Не удалось обновить профиль",
      profileUpdatedSuccessfully: "Профиль обновлен успешно",
      errorOccurred: "Произошла ошибка при обновлении профиля",
    },
  };

  const { locale, show, onHide, onProfileUpdated, userData }: Props = $props();

  const t = $derived(i18n[locale]);

  let error = $state("");
  let isSubmitting = $state(false);
  let submitSuccess = $state(false);

  let name = $derived(userData?.name || []);
  let description = $derived(userData?.description || []);

  const handleSubmit = async () => {
    if (!name.some((item) => item.value.trim().length)) {
      error = t.nameRequired;

      return;
    }

    isSubmitting = true;
    error = "";

    try {
      const response = await fetchWithAuth(`/api/user/${userData?.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          description,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToUpdateProfile);
      }

      submitSuccess = true;
      onProfileUpdated();

      // Close modal after a short delay
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  };

  const handleClose = () => {
    error = "";
    submitSuccess = false;
    onHide();
  };
</script>

<Modal
  {show}
  title={t.editProfile}
  onClose={handleClose}
  onSubmit={handleSubmit}
  submitText={isSubmitting ? t.saving : t.saveChanges}
  cancelText={t.cancel}
  submitDisabled={!name.some((item) => item.value.trim().length) || isSubmitting}
  cancelDisabled={isSubmitting}
  {isSubmitting}
>
  {#if submitSuccess}
    <div class="alert alert-success mb-3">
      {t.profileUpdatedSuccessfully}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  <form
    onsubmit={(e) => {
      e.preventDefault();
      handleSubmit();
    }}
  >
    <LocalizedInput
      id="profileName"
      label={t.name.label}
      placeholder={t.name.placeholder}
      required={true}
      {locale}
      bind:value={name}
    />

    <LocalizedTextarea
      id="profileDescription"
      label={t.description.label}
      placeholder={t.description.placeholder}
      rows={4}
      {locale}
      bind:value={description}
    />
  </form>
</Modal>
