"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VotingService = void 0;
const common_1 = require("@nestjs/common");
const utils_1 = require("../utils");
const base_service_1 = require("../common/base-service");
const prisma_service_1 = require("../prisma/prisma.service");
let VotingService = class VotingService extends base_service_1.BaseService {
    constructor(prisma) {
        super("voting");
        this.prisma = prisma;
    }
    async canGet(id, user) {
        await this.getOneOrThrow(id);
        return true;
    }
    async check(ids) {
        return await this._check(ids, this.prisma.voting, "voting");
    }
    async getOne(id) {
        return await this.prisma.voting.findUnique({
            where: { id, deletedAt: null },
            include: {
                title: true,
                description: true,
                options: {
                    include: {
                        title: true,
                    },
                },
            },
        });
    }
    async getOneOrThrow(id) {
        const voting = await this.getOne(id);
        if (!voting) {
            throw this.createNotFoundException();
        }
        return voting;
    }
    async getMany(where, pagination) {
        return await this.prisma.voting.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
            include: {
                title: true,
                description: true,
                options: {
                    include: {
                        title: true,
                    },
                },
            },
        });
    }
    async createOne(data) {
        return await this.prisma.voting.create({ data });
    }
    async createMany(data) {
        return await this.prisma.voting.createMany({ data });
    }
    async create(data) {
        return await this.prisma.voting.create({
            data: {
                votesRequired: data.votesRequired,
                endsAt: data.endsAt,
                title: {
                    create: data.title.map((item) => ({
                        ...item,
                        key: "title",
                    })),
                },
                description: {
                    create: data.description.map((item) => ({
                        ...item,
                        key: "description",
                    })),
                },
                options: {
                    create: data.options.map((option) => ({
                        title: {
                            create: option.title,
                        },
                    })),
                },
            },
            include: {
                title: true,
                description: true,
                options: {
                    include: {
                        title: true,
                    },
                },
            },
        });
    }
    async updateOne(id, data) {
        return await this.prisma.voting.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }
    async updateMany(where, data) {
        return await this.prisma.voting.updateMany({ where, data });
    }
    async softDeleteOne(id) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }
    async softDeleteMany(where) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }
    async deleteOne(id) {
        return await this.prisma.voting.delete({ where: { id } });
    }
    async deleteMany(where) {
        return await this.prisma.voting.deleteMany({ where });
    }
};
exports.VotingService = VotingService;
exports.VotingService = VotingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], VotingService);
//# sourceMappingURL=voting.service.js.map