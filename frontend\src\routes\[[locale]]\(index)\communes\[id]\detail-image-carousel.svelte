<script lang="ts">
  import type { Common } from "@commune/api";

  import { onMount } from "svelte";

  interface Props {
    locale: Common.LocalizationLocale;
    images: Common.Images;
  }

  const i18n = {
    en: {
      noImages: "No images available",
      communeImageAlt: "Commune image",
      previous: "Previous",
      next: "Next",
    },
    ru: {
      noImages: "Нет доступных изображений",
      communeImageAlt: "Изображение коммуны",
      previous: "Предыдущий",
      next: "Следующий",
    },
  };

  const { locale, images }: Props = $props();

  const t = $derived(i18n[locale]);

  let activeIndex = $state(0);
  let carouselElement = $state<HTMLElement | null>(null);

  function nextSlide() {
    activeIndex = (activeIndex + 1) % images.length;
  }

  function prevSlide() {
    activeIndex = (activeIndex - 1 + images.length) % images.length;
  }

  function goToSlide(index: number) {
    activeIndex = index;
  }

  // Set up carousel functionality
  onMount(() => {
    if (carouselElement && images.length > 1) {
      // Optional: Add event listeners for keyboard navigation
      const handleKeydown = (e: KeyboardEvent) => {
        if (e.key === "ArrowLeft") prevSlide();
        if (e.key === "ArrowRight") nextSlide();
      };

      document.addEventListener("keydown", handleKeydown);

      return () => {
        document.removeEventListener("keydown", handleKeydown);
      };
    }
  });
</script>

{#if !images || images.length === 0}
  <div
    class="bg-light text-center rounded"
    style="height: 300px; display: flex; align-items: center; justify-content: center;"
  >
    <span class="text-muted">{t.noImages}</span>
  </div>
{:else}
  <div class="carousel-container">
    <div id="detailImageCarousel" class="carousel slide" bind:this={carouselElement}>
      <!-- Carousel items -->
      <div class="carousel-inner">
        {#each images as image, index}
          <div class={`carousel-item ${activeIndex === index ? "active" : ""}`}>
            <div class="image-container">
              <img
                src={`/api/images/${image.url}`}
                alt={`${t.communeImageAlt} ${index + 1}`}
                class="carousel-image"
              />
            </div>
          </div>
        {/each}
      </div>

      {#if images.length > 1}
        <!-- Carousel controls -->
        <button
          class="carousel-control-prev"
          type="button"
          onclick={prevSlide}
          aria-label={t.previous}
        >
          <span class="carousel-control-prev-icon" aria-hidden="true"></span>
          <span class="visually-hidden">{t.previous}</span>
        </button>
        <button class="carousel-control-next" type="button" onclick={nextSlide} aria-label={t.next}>
          <span class="carousel-control-next-icon" aria-hidden="true"></span>
          <span class="visually-hidden">{t.next}</span>
        </button>

        <!-- Custom indicators -->
        <div class="indicators">
          {#each images as _, index}
            <button
              class={`indicator ${activeIndex === index ? "active-indicator" : ""}`}
              onclick={() => goToSlide(index)}
              aria-label={`Slide ${index + 1}`}
            ></button>
          {/each}
        </div>
      {/if}
    </div>
  </div>
{/if}

<style>
  .carousel-container {
    position: relative;
    height: 300px;
    overflow: hidden;
    border-radius: 0.375rem;
    margin-bottom: 1.5rem;
  }

  .carousel-inner {
    height: 100%;
  }

  .carousel-item {
    height: 300px;
  }

  .image-container {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .carousel-control-prev,
  .carousel-control-next {
    width: 10%;
    background: rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .carousel-container:hover .carousel-control-prev,
  .carousel-container:hover .carousel-control-next {
    opacity: 1;
  }

  .indicators {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 8px;
    z-index: 2;
  }

  .indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    padding: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .active-indicator {
    background-color: #fff;
  }
</style>
