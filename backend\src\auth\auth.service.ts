import { z } from "zod";
import { Injectable, UnauthorizedException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as prisma from "@prisma/client";
import { getError } from "src/common/errors";
import { EmailService } from "src/email/email.service";
import { UserService } from "src/user/user.service";
import { EmailOtpService } from "src/email/email-otp.service";

type InfoDto = {
    ipAddress: string | null;
    userAgent: string | null;
};

type OtpDto = InfoDto & {
    email: string;
};

type RegisterDto = InfoDto & {
    referrerId: string | null;
    email: string;
    otp: string;
};

type LoginDto = InfoDto & {
    email: string;
    otp: string;
};

// type LogoutDto = InfoDto & {
//     refreshToken: string;
// };

const coerceBoolean = (data: unknown) => z.coerce.boolean().parse(data);
const nonEmptyString = (data: unknown) => z.string().nonempty().parse(data);

@Injectable()
export class AuthService {
    private readonly disableRegisterOtpCheck: boolean;
    private readonly disableLoginOtpCheck: boolean;

    private readonly instanceName: string;
    private readonly domain: string;
    private readonly otpSender: string;

    constructor(
        private readonly configService: ConfigService,
        private readonly emailService: EmailService,
        private readonly userService: UserService,
        private readonly userOtpService: EmailOtpService,
    ) {
        this.disableRegisterOtpCheck = coerceBoolean(
            this.configService.get("DISABLE_REGISTER_OTP_CHECK"),
        );

        this.disableLoginOtpCheck = coerceBoolean(
            this.configService.get("DISABLE_LOGIN_OTP_CHECK"),
        );

        this.instanceName = nonEmptyString(
            this.configService.get("INSTANCE_NAME"),
        );

        this.domain = nonEmptyString(
            this.configService.get("INSTANCE_EMAIL_DOMAIN"),
        );

        this.otpSender = nonEmptyString(
            this.configService.get("OTP_EMAIL_SENDER"),
        );
    }

    protected createSessionUser(user: prisma.User) {
        return {
            id: user.id,
            email: user.email,
            role: user.role,
        };
    }

    protected generateOtp() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }

    async otp(dto: OtpDto) {
        const otp = this.generateOtp();

        await this.userOtpService.create({
            email: dto.email,
            otp,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });

        console.log({ otp, email: dto.email });

        return await this.emailService.send({
            from: this.emailService.joinAddress(this.otpSender, this.domain),
            to: [dto.email],
            subject: `${this.instanceName} - OTP`,
            text: `Your OTP is ${otp}.`,
        });
    }

    async login(dto: LoginDto) {
        const user = await this.userService.getByEmail(dto.email);

        if (!user) {
            throw new UnauthorizedException(...getError("user_not_found"));
        }

        if (!this.disableLoginOtpCheck) {
            const userOtp = await this.userOtpService.check({
                email: dto.email,
                otp: dto.otp,
            });

            await this.userOtpService.softDelete({
                id: userOtp.id,
            });
        }

        const sessionUser = this.createSessionUser(user);

        return {
            user: sessionUser,
        };
    }

    async register(dto: RegisterDto) {
        // check for duplicates
        {
            const user = await this.userService.getByEmail(dto.email);

            if (user) {
                throw new UnauthorizedException(
                    ...getError("user_already_exists"),
                );
            }
        }

        if (!this.disableRegisterOtpCheck) {
            const otp = await this.userOtpService.check({
                email: dto.email,
                otp: dto.otp,
            });

            await this.userOtpService.softDelete({
                id: otp.id,
            });
        }

        const user = await this.userService.create({
            referrerId: dto.referrerId,
            email: dto.email,
        });

        const sessionUser = this.createSessionUser(user);

        return {
            user: sessionUser,
        };
    }
}
