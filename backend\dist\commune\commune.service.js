"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const utils_1 = require("../utils");
const errors_1 = require("../common/errors");
const base_service_1 = require("../common/base-service");
const prisma_service_1 = require("../prisma/prisma.service");
const minio_service_1 = require("../minio/minio.service");
const WEEK = 7 * 24 * 60 * 60 * 1000;
const INVITATION_EXPIRATION_TIME = WEEK;
let CommuneService = class CommuneService extends base_service_1.BaseService {
    constructor(prisma, minioService) {
        super("commune");
        this.prisma = prisma;
        this.minioService = minioService;
    }
    async canGet(id, user) {
        await this.getOneOrThrow(id);
        return true;
    }
    async canChange(id, user) {
        await this.getOneOrThrow(id);
        const headMember = await this.getHeadMember(id);
        if (user.role !== client_1.UserRole.admin) {
            if (headMember.actorType === client_1.CommuneMemberType.user &&
                headMember.actorId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_head_member"));
            }
        }
        return true;
    }
    async check(ids) {
        return await this._check(ids, this.prisma.commune, this.entityType);
    }
    isHeadMember(commune, userId) {
        return commune.members.some((member) => member.isHead &&
            member.actorId === userId &&
            member.actorType === client_1.CommuneMemberType.user);
    }
    isMember(commune, userId) {
        return commune.members.some((member) => member.actorId === userId &&
            member.actorType === client_1.CommuneMemberType.user);
    }
    async getOne(id) {
        return await this.prisma.commune.findUnique({
            where: {
                id,
                deletedAt: null,
            },
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }
    async getOneOrThrow(id) {
        const commune = await this.getOne(id);
        if (!commune) {
            throw this.createNotFoundException();
        }
        return commune;
    }
    async getMany(where, pagination) {
        return await this.prisma.commune.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where,
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }
    async createOne(data) {
        return await this.prisma.commune.create({
            data,
        });
    }
    async createMany(data) {
        return await this.prisma.commune.createMany({
            data,
        });
    }
    async create(data, user) {
        if (!data.headUserId) {
            data.headUserId = user.id;
        }
        if (data.headUserId !== user.id) {
            if (user.role !== "admin") {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("head_user_id_mismatch"));
            }
        }
        const commune = await this.prisma.commune.create({
            data: {
                members: {
                    create: {
                        actorType: client_1.CommuneMemberType.user,
                        actorId: data.headUserId,
                        isHead: true,
                    },
                },
                name: {
                    create: data.name.map((item) => ({
                        ...item,
                        key: "name",
                    })),
                },
                description: {
                    create: data.description.map((item) => ({
                        ...item,
                        key: "description",
                    })),
                },
            },
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
        return commune;
    }
    async uploadCommuneImages(communeId, files) {
        await this.getOneOrThrow(communeId);
        const images = await Promise.all(files.map(async (file, index) => {
            const imageUrl = await this.minioService.uploadCommuneImage(file, communeId, index);
            const image = await this.prisma.image.create({
                data: {
                    url: imageUrl,
                },
            });
            return image;
        }));
        await this.prisma.commune.update({
            where: { id: communeId },
            data: {
                images: {
                    connect: images.map((image) => ({ id: image.id })),
                },
            },
        });
        return images;
    }
    async updateOne(id, data) {
        return await this.prisma.commune.update({
            where: { id },
            data,
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }
    async getHeadMember(id) {
        return await this.prisma.communeMember.findFirstOrThrow({
            where: {
                communeId: id,
                isHead: true,
                deletedAt: null,
            },
        });
    }
    async update(id, data, user) {
        const commune = await this.getOne(id);
        if (!commune) {
            throw this.createNotFoundException();
        }
        if (user.role !== "admin") {
            const headMember = await this.getHeadMember(id);
            if (headMember.actorType === client_1.CommuneMemberType.user &&
                headMember.actorId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_head_member"));
            }
        }
        return await this.updateOne(id, data);
    }
    async updateMany(where, data) {
        return await this.prisma.commune.updateMany({ where, data });
    }
    async softDeleteOne(id) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }
    async softDeleteOneCascade(id, user) {
        const commune = await this.getOneOrThrow(id);
        if (!user.isAdmin && !this.isHeadMember(commune, user.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
        }
        await this.prisma.$transaction(async (trx) => {
            const now = new Date();
            await trx.commune.update({
                where: { id },
                data: { deletedAt: now },
            });
            await trx.communeMember.updateMany({
                where: { communeId: id },
                data: { deletedAt: now },
            });
        });
    }
    async softDeleteMany(where) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }
    async deleteOne(id) {
        return await this.prisma.commune.delete({ where: { id } });
    }
    async deleteMany(where) {
        return await this.prisma.commune.deleteMany({ where });
    }
    async getInvitationsForUser(userId, pagination) {
        return await this.prisma.communeInvitation.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                userId,
                deletedAt: null,
            },
        });
    }
    async getInvitationsForCommune(communeId, pagination) {
        return await this.prisma.communeInvitation.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                communeId,
                deletedAt: null,
            },
        });
    }
    async createInvitation(communeId, userId, currentUser) {
        const commune = await this.getOneOrThrow(communeId);
        if (currentUser.role !== "admin" &&
            !this.isHeadMember(commune, currentUser.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
        }
        if (this.isMember(commune, userId)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("user_already_in_commune"));
        }
        const existingInvitation = await this.prisma.communeInvitation.findFirst({
            where: {
                communeId,
                userId,
                status: client_1.CommuneInvitationStatus.pending,
                deletedAt: null,
            },
        });
        if (existingInvitation) {
            return {
                id: existingInvitation.id,
            };
        }
        const invitation = await this.prisma.communeInvitation.create({
            data: {
                communeId,
                userId,
                expiresAt: new Date(Date.now() + INVITATION_EXPIRATION_TIME),
            },
        });
        return {
            id: invitation.id,
        };
    }
    async deleteInvitation(id, currentUser) {
        const invitation = await this.prisma.communeInvitation.findUniqueOrThrow({
            where: { id },
        });
        if (invitation.status !== client_1.CommuneInvitationStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("invitation_must_be_pending"));
        }
        const commune = await this.getOneOrThrow(invitation.communeId);
        if (currentUser.role !== "admin" &&
            !this.isHeadMember(commune, currentUser.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
        }
        await this.prisma.communeInvitation.update({
            where: { id },
            data: {
                deletedAt: new Date(),
            },
        });
        return true;
    }
    async acceptInvitation(id, currentUser) {
        const invitation = await this.prisma.communeInvitation.findUniqueOrThrow({
            where: { id },
        });
        if (invitation.status !== client_1.CommuneInvitationStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("invitation_must_be_pending"));
        }
        if (!currentUser.isAdmin && invitation.userId !== currentUser.id) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_invitation_target"));
        }
        const commune = await this.getOneOrThrow(invitation.communeId);
        if (this.isMember(commune, currentUser.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("user_already_in_commune"));
        }
        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.create({
                data: {
                    communeId: invitation.communeId,
                    actorType: client_1.CommuneMemberType.user,
                    actorId: currentUser.id,
                    isHead: false,
                },
            });
            await trx.communeInvitation.update({
                where: { id },
                data: {
                    status: client_1.CommuneInvitationStatus.accepted,
                },
            });
        });
        return true;
    }
    async rejectInvitation(id, currentUser) {
        const invitation = await this.prisma.communeInvitation.findUniqueOrThrow({
            where: { id },
        });
        if (invitation.status !== client_1.CommuneInvitationStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("invitation_must_be_pending"));
        }
        if (!currentUser.isAdmin && invitation.userId !== currentUser.id) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_invitation_target"));
        }
        const commune = await this.getOneOrThrow(invitation.communeId);
        if (this.isMember(commune, currentUser.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("user_already_in_commune"));
        }
        await this.prisma.communeInvitation.update({
            where: { id },
            data: {
                status: client_1.CommuneInvitationStatus.rejected,
            },
        });
        return true;
    }
    async getJoinRequestsForUser(userId, pagination) {
        return await this.prisma.communeJoinRequest.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                userId,
                deletedAt: null,
            },
        });
    }
    async getJoinRequestsForCommune(communeId, pagination) {
        return await this.prisma.communeJoinRequest.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                communeId,
                deletedAt: null,
            },
        });
    }
    async createJoinRequest(communeId, userId, currentUser) {
        const commune = await this.getOneOrThrow(communeId);
        if (!currentUser.isAdmin && currentUser.id !== userId) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_self"));
        }
        if (this.isMember(commune, userId)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("user_already_in_commune"));
        }
        const existingJoinRequest = await this.prisma.communeJoinRequest.findFirst({
            where: {
                communeId,
                userId,
                status: client_1.CommuneJoinRequestStatus.pending,
                deletedAt: null,
            },
        });
        if (existingJoinRequest) {
            return {
                id: existingJoinRequest.id,
            };
        }
        const joinRequest = await this.prisma.communeJoinRequest.create({
            data: {
                communeId,
                userId,
            },
        });
        return {
            id: joinRequest.id,
        };
    }
    async deleteJoinRequest(id, currentUser) {
        const joinRequest = await this.prisma.communeJoinRequest.findUniqueOrThrow({
            where: { id },
        });
        if (joinRequest.status !== client_1.CommuneJoinRequestStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("join_request_must_be_pending"));
        }
        if (!currentUser.isAdmin && joinRequest.userId !== currentUser.id) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_join_request_target"));
        }
        await this.prisma.communeJoinRequest.update({
            where: { id },
            data: {
                deletedAt: new Date(),
            },
        });
        return true;
    }
    async acceptJoinRequest(id, currentUser) {
        const joinRequest = await this.prisma.communeJoinRequest.findUniqueOrThrow({
            where: { id },
        });
        if (joinRequest.status !== client_1.CommuneJoinRequestStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("join_request_must_be_pending"));
        }
        const commune = await this.getOneOrThrow(joinRequest.communeId);
        if (!currentUser.isAdmin &&
            !this.isHeadMember(commune, currentUser.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
        }
        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.create({
                data: {
                    communeId: joinRequest.communeId,
                    actorType: client_1.CommuneMemberType.user,
                    actorId: joinRequest.userId,
                    isHead: false,
                },
            });
            await this.prisma.communeJoinRequest.update({
                where: { id },
                data: {
                    status: client_1.CommuneJoinRequestStatus.accepted,
                },
            });
        });
        return true;
    }
    async rejectJoinRequest(id, currentUser) {
        const joinRequest = await this.prisma.communeJoinRequest.findUniqueOrThrow({
            where: { id },
        });
        if (joinRequest.status !== client_1.CommuneJoinRequestStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("join_request_must_be_pending"));
        }
        const commune = await this.getOneOrThrow(joinRequest.communeId);
        if (!currentUser.isAdmin &&
            !this.isHeadMember(commune, currentUser.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
        }
        await this.prisma.communeJoinRequest.update({
            where: { id },
            data: {
                status: client_1.CommuneJoinRequestStatus.rejected,
            },
        });
        return true;
    }
    async transferHeadStatus(communeId, newHeadUserId, currentUser) {
        const commune = await this.getOneOrThrow(communeId);
        if (!currentUser.isAdmin &&
            !this.isHeadMember(commune, currentUser.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
        }
        if (!this.isMember(commune, newHeadUserId)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_member"));
        }
        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.updateMany({
                where: {
                    communeId,
                },
                data: {
                    isHead: false,
                },
            });
            await trx.communeMember.updateMany({
                where: {
                    communeId,
                    actorType: client_1.CommuneMemberType.user,
                    actorId: newHeadUserId,
                },
                data: {
                    isHead: true,
                },
            });
        });
        return true;
    }
};
exports.CommuneService = CommuneService;
exports.CommuneService = CommuneService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        minio_service_1.MinioService])
], CommuneService);
//# sourceMappingURL=commune.service.js.map