"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const zod_1 = require("../../zod");
const errors_1 = require("../../common/errors");
const session_auth_guard_1 = require("../../auth/http/session-auth.guard");
const current_user_decorator_1 = require("../../auth/http/current-user.decorator");
const commune_service_1 = require("../commune.service");
const file_upload_dto_1 = require("./file-upload.dto");
const commune_member_service_1 = require("../commune-member.service");
const user_service_1 = require("../../user/user.service");
const api_1 = require("@commune/api");
let CommuneController = class CommuneController {
    constructor(communeService, communeMemberService, userService) {
        this.communeService = communeService;
        this.communeMemberService = communeMemberService;
        this.userService = userService;
    }
    async getInvitations(user, pagination) {
        const invitations = await this.communeService.getInvitationsForUser(user.id, pagination);
        return api_1.Common.parseInput(api_1.Commune.CommuneInvitationsSchema, invitations);
    }
    async getInvitationsForCommune(id, pagination) {
        const invitations = await this.communeService.getInvitationsForCommune(id, pagination);
        return api_1.Common.parseInput(api_1.Commune.CommuneInvitationsSchema, invitations);
    }
    async createInvitation(body, user) {
        const result = await this.communeService.createInvitation(body.communeId, body.userId, user);
        return api_1.Common.parseInput(api_1.Common.ObjectWithIdSchema, result);
    }
    async deleteInvitation(invitationId, user) {
        await this.communeService.deleteInvitation(invitationId, user);
    }
    async acceptInvitation(invitationId, user) {
        await this.communeService.acceptInvitation(invitationId, user);
    }
    async rejectInvitation(invitationId, user) {
        await this.communeService.rejectInvitation(invitationId, user);
    }
    async getJoinRequests(user, pagination) {
        const joinRequests = await this.communeService.getJoinRequestsForUser(user.id, pagination);
        return api_1.Common.parseInput(api_1.Commune.CommuneJoinRequestsSchema, joinRequests);
    }
    async getJoinRequestsForCommune(id, pagination) {
        const joinRequests = await this.communeService.getJoinRequestsForCommune(id, pagination);
        return api_1.Common.parseInput(api_1.Commune.CommuneJoinRequestsSchema, joinRequests);
    }
    async createJoinRequest(body, user) {
        const result = await this.communeService.createJoinRequest(body.communeId, body.userId, user);
        return api_1.Common.parseInput(api_1.Common.ObjectWithIdSchema, result);
    }
    async deleteJoinRequest(joinRequestId, user) {
        await this.communeService.deleteJoinRequest(joinRequestId, user);
    }
    async acceptJoinRequest(joinRequestId, user) {
        await this.communeService.acceptJoinRequest(joinRequestId, user);
    }
    async rejectJoinRequest(joinRequestId, user) {
        await this.communeService.rejectJoinRequest(joinRequestId, user);
    }
    async getCommuneToReturn(commune) {
        const headMember = commune.members.find((member) => member.isHead);
        if (!headMember) {
            throw new common_1.InternalServerErrorException((0, errors_1.getError)("commune_head_member_not_found"));
        }
        const headMemberEntity = headMember.actorType === "user"
            ? await this.userService.getOne(headMember.actorId)
            : await this.communeService.getOne(headMember.actorId);
        if (!headMemberEntity) {
            throw new common_1.InternalServerErrorException((0, errors_1.getError)("commune_head_member_entity_not_found"));
        }
        const memberCount = commune.members.length;
        return {
            ...commune,
            headMember: {
                actorType: headMember.actorType,
                actorId: headMember.actorId,
                name: headMemberEntity.name,
            },
            memberCount,
            images: commune.images || [],
        };
    }
    async getCommunes(pagination) {
        const communes = await this.communeService.getMany({
            deletedAt: null,
        }, pagination);
        const communesToReturn = await Promise.all(communes.map((commune) => this.getCommuneToReturn(commune)));
        return api_1.Common.parseInput(api_1.Commune.CommunesSchema, communesToReturn);
    }
    async createCommune(body, user, files) {
        try {
            const commune = await this.communeService.create({
                headUserId: body.headUserId,
                name: body.name,
                description: body.description,
            }, user);
            if (files && files.length > 0) {
                try {
                    await this.communeService.uploadCommuneImages(commune.id, files);
                }
                catch (error) {
                    console.error("Failed to upload images:", error);
                }
            }
            return api_1.Common.parseInput(api_1.Commune.CommuneSchema, await this.getCommuneToReturn(commune));
        }
        catch (error) {
            console.error("Error processing form data:", error);
            if (error instanceof zod_1.z.ZodError) {
                throw new common_1.BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }
            throw new common_1.BadRequestException("Failed to process form data");
        }
    }
    async uploadCommuneImages(id, user, files) {
        try {
            await this.communeService.canChange(id, user);
            if (!files || files.length === 0) {
                throw new common_1.BadRequestException("No files uploaded");
            }
            const images = await this.communeService.uploadCommuneImages(id, files);
            return api_1.Common.parseInput(api_1.Common.ImagesSchema, images);
        }
        catch (error) {
            console.error("Error uploading images:", error);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException("Failed to upload images");
        }
    }
    async getCommune(id) {
        const commune = await this.communeService.getOne(id);
        if (!commune) {
            throw new common_1.NotFoundException((0, errors_1.getError)("commune_not_found"));
        }
        return api_1.Common.parseInput(api_1.Commune.CommuneSchema, await this.getCommuneToReturn(commune));
    }
    async updateCommune(id, body, user) {
        try {
            const commune = await this.communeService.getOne(id);
            if (!commune) {
                throw new common_1.NotFoundException((0, errors_1.getError)("commune_not_found"));
            }
            const updatedCommune = await this.communeService.update(id, {
                name: {
                    deleteMany: {},
                    create: body.name.map((item) => ({
                        key: "name",
                        locale: item.locale,
                        value: item.value,
                    })),
                },
                description: {
                    deleteMany: {},
                    create: body.description.map((item) => ({
                        key: "description",
                        locale: item.locale,
                        value: item.value,
                    })),
                },
            }, user);
            return api_1.Common.parseInput(api_1.Commune.CommuneSchema, await this.getCommuneToReturn(updatedCommune));
        }
        catch (error) {
            console.error("Error updating commune:", error);
            if (error instanceof zod_1.z.ZodError) {
                throw new common_1.BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException("Failed to update commune");
        }
    }
    async deleteCommune(id, user) {
        await this.communeService.softDeleteOneCascade(id, user);
    }
    async getCommuneMembers(id, pagination) {
        const communeMembers = await this.communeMemberService.getMany({
            communeId: id,
            deletedAt: null,
        }, pagination);
        return api_1.Common.parseInput(api_1.Commune.CommuneMembersSchema, communeMembers);
    }
    async getCommuneMember(memberId) {
        const communeMember = await this.communeMemberService.getOne(memberId);
        if (!communeMember) {
            throw new common_1.NotFoundException((0, errors_1.getError)("commune_member_not_found"));
        }
        return api_1.Common.parseInput(api_1.Commune.CommuneMemberSchema, communeMember);
    }
    async createCommuneMember(id, body) {
        const commune = await this.communeMemberService.createOne({
            commune: {
                connect: {
                    id,
                    deletedAt: null,
                },
            },
            actorType: "user",
            actorId: body.userId,
        });
        return api_1.Common.parseInput(api_1.Commune.CommuneMemberSchema, commune);
    }
    async deleteCommuneMember(memberId) {
        await this.communeMemberService.softDeleteOne(memberId);
        return true;
    }
    async transferHeadStatus(id, body, user) {
        await this.communeService.transferHeadStatus(id, body.newHeadUserId, user);
    }
};
exports.CommuneController = CommuneController;
__decorate([
    (0, common_1.Get)("invitation"),
    __param(0, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(1, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getInvitations", null);
__decorate([
    (0, common_1.Get)(":id/invitation"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getInvitationsForCommune", null);
__decorate([
    (0, common_1.Put)("invitation"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Commune.CreateCommuneInvitationRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "createInvitation", null);
__decorate([
    (0, common_1.Delete)("invitation/:invitationId"),
    __param(0, (0, common_1.Param)("invitationId", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "deleteInvitation", null);
__decorate([
    (0, common_1.Post)("invitation/:invitationId/accept"),
    __param(0, (0, common_1.Param)("invitationId", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "acceptInvitation", null);
__decorate([
    (0, common_1.Post)("invitation/:invitationId/reject"),
    __param(0, (0, common_1.Param)("invitationId", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "rejectInvitation", null);
__decorate([
    (0, common_1.Get)("join-request"),
    __param(0, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(1, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getJoinRequests", null);
__decorate([
    (0, common_1.Get)(":id/join-request"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getJoinRequestsForCommune", null);
__decorate([
    (0, common_1.Put)("join-request"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Commune.CreateCommuneJoinRequestRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "createJoinRequest", null);
__decorate([
    (0, common_1.Delete)("join-request/:joinRequestId"),
    __param(0, (0, common_1.Param)("joinRequestId", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "deleteJoinRequest", null);
__decorate([
    (0, common_1.Post)("join-request/:joinRequestId/accept"),
    __param(0, (0, common_1.Param)("joinRequestId", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "acceptJoinRequest", null);
__decorate([
    (0, common_1.Post)("join-request/:joinRequestId/reject"),
    __param(0, (0, common_1.Param)("joinRequestId", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "rejectJoinRequest", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getCommunes", null);
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)("images", file_upload_dto_1.MAX_FILES_COUNT)),
    __param(0, (0, common_1.Body)("data", new zod_1.ZodPipe(api_1.Commune.CreateCommuneRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFiles)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: file_upload_dto_1.MAX_FILE_SIZE }),
            new common_1.FileTypeValidator({
                fileType: file_upload_dto_1.ALLOWED_FILE_TYPES.join("|"),
            }),
        ],
        fileIsRequired: false,
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Array]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "createCommune", null);
__decorate([
    (0, common_1.Post)(":id/images"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)("images", file_upload_dto_1.MAX_FILES_COUNT)),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFiles)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: file_upload_dto_1.MAX_FILE_SIZE }),
            new common_1.FileTypeValidator({
                fileType: file_upload_dto_1.ALLOWED_FILE_TYPES.join("|"),
            }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Array]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "uploadCommuneImages", null);
__decorate([
    (0, common_1.Get)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getCommune", null);
__decorate([
    (0, common_1.Put)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Commune.UpdateCommuneRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "updateCommune", null);
__decorate([
    (0, common_1.Delete)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "deleteCommune", null);
__decorate([
    (0, common_1.Get)(":id/member"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getCommuneMembers", null);
__decorate([
    (0, common_1.Get)(":id/member/:memberId"),
    __param(0, (0, common_1.Param)("memberId", new zod_1.ZodPipe(api_1.Common.id))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getCommuneMember", null);
__decorate([
    (0, common_1.Post)(":id/member"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Commune.CreateCommuneMemberRequestSchema))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "createCommuneMember", null);
__decorate([
    (0, common_1.Delete)(":id/member/:memberId"),
    __param(0, (0, common_1.Param)("memberId", new zod_1.ZodPipe(api_1.Common.id))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "deleteCommuneMember", null);
__decorate([
    (0, common_1.Post)(":id/transfer-head-status"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Commune.TransferHeadStatusRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "transferHeadStatus", null);
exports.CommuneController = CommuneController = __decorate([
    (0, common_1.Controller)("commune"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __metadata("design:paramtypes", [commune_service_1.CommuneService,
        commune_member_service_1.CommuneMemberService,
        user_service_1.UserService])
], CommuneController);
//# sourceMappingURL=commune.controller.js.map