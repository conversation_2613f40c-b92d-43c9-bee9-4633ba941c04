import type { z } from "zod";

export type NonNullableUnknown = NonNullable<unknown>;

export type SuggestableString<T extends string> =
    | T
    | (string & NonNullableUnknown);

export type Satisfies<T, U extends T> = U;

export type Normalize<T> = { [K in keyof T]: T[K] } & NonNullableUnknown;

export type Infer<T extends z.ZodTypeAny> = Normalize<z.infer<T>>;

export type InferObject<T extends Record<string, z.ZodType>> = Normalize<{
    [K in keyof T]: z.infer<T[K]>;
}>;
