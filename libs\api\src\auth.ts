import type { Infer } from "./types";

import { z } from "zod";
import { email, id, ImagesSchema } from "./common";
import { userDescription, userName, UserRoleSchema } from "./user";

export const otp = z.string().nonempty().length(6);

export type SendOtpRequest = Infer<typeof SendOtpRequestSchema>;
export const SendOtpRequestSchema = z.object({
    email,
});

export type SendOtpResponse = Infer<typeof SendOtpResponseSchema>;
export const SendOtpResponseSchema = z.object({
    isSent: z.boolean(),
});

export type GetMeResponse = Infer<typeof GetMeResponseSchema>;
export const GetMeResponseSchema = z.object({
    id,
    email,
    role: UserRoleSchema,

    name: userName,
    description: userDescription,

    images: ImagesSchema,

    createdAt: z.date(),
    updatedAt: z.date(),
});

export type RegisterRequest = Infer<typeof RegisterRequestSchema>;
export const RegisterRequestSchema = z.object({
    referrerId: id.nullable(),
    email,
    otp,
});

export type LoginRequest = Infer<typeof LoginRequestSchema>;
export const LoginRequestSchema = z.object({
    email,
    otp,
});

export type SuccessfulAuthResponse = Infer<typeof SuccessfulAuthResponseSchema>;
export const SuccessfulAuthResponseSchema = z.object({
    id,
    email,
    role: UserRoleSchema,
});
