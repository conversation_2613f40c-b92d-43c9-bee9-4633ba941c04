import { ConfigService } from "@nestjs/config";
type SendEmailDto = {
    from: string;
    to: string[];
    subject: string;
    text: string;
};
export declare class EmailService {
    private readonly configService;
    private readonly transporter;
    private readonly isIgnoreEmailErrors;
    private readonly isDisableOtpEmail;
    constructor(configService: ConfigService);
    joinAddress(sender: string, domain: string): string;
    send(dto: SendEmailDto): Promise<boolean>;
}
export {};
