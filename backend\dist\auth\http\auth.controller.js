"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const zod_1 = require("../../zod");
const user_service_1 = require("../../user/user.service");
const auth_service_1 = require("../auth.service");
const session_auth_guard_1 = require("./session-auth.guard");
const current_user_decorator_1 = require("./current-user.decorator");
const api_1 = require("@commune/api");
let AuthController = class AuthController {
    constructor(authService, userService) {
        this.authService = authService;
        this.userService = userService;
    }
    async test() {
        return true;
    }
    async me(currentUser) {
        const user = await this.userService.getOne(currentUser.id);
        if (!user) {
            throw new common_1.UnauthorizedException();
        }
        return api_1.Common.parseInput(api_1.Auth.GetMeResponseSchema, {
            id: user.id,
            email: user.email,
            role: user.role,
            name: user.name,
            description: user.description,
            images: user.images,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
        });
    }
    async otp(body, ipAddress, userAgent) {
        const isSent = await this.authService.otp({
            ...body,
            ipAddress,
            userAgent,
        });
        return api_1.Common.parseInput(api_1.Auth.SendOtpResponseSchema, {
            isSent,
        });
    }
    async register(req, body, ipAddress, userAgent) {
        const { user } = await this.authService.register({
            ...body,
            referrerId: body.referrerId ?? null,
            ipAddress,
            userAgent,
        });
        req.session.user = user;
        return api_1.Common.parseInput(api_1.Auth.SuccessfulAuthResponseSchema, user);
    }
    async login(req, body, ipAddress, userAgent) {
        const { user } = await this.authService.login({
            ...body,
            ipAddress,
            userAgent,
        });
        req.session.user = user;
        return api_1.Common.parseInput(api_1.Auth.SuccessfulAuthResponseSchema, user);
    }
    async signOut(req, res) {
        req.session.destroy((err) => {
            if (err) {
                console.error("Error destroying session:", err);
            }
            res.clearCookie("session");
            res.sendStatus(common_1.HttpStatus.OK);
        });
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Get)("test"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "test", null);
__decorate([
    (0, common_1.Get)("me"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __param(0, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "me", null);
__decorate([
    (0, common_1.Post)("otp"),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Auth.SendOtpRequestSchema))),
    __param(1, (0, common_1.Ip)()),
    __param(2, (0, common_1.Headers)("user-agent")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "otp", null);
__decorate([
    (0, common_1.Post)("register"),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Auth.RegisterRequestSchema))),
    __param(2, (0, common_1.Ip)()),
    __param(3, (0, common_1.Headers)("user-agent")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String, String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, common_1.Post)("login"),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Auth.LoginRequestSchema))),
    __param(2, (0, common_1.Ip)()),
    __param(3, (0, common_1.Headers)("user-agent")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String, String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Get)("sign-out"),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "signOut", null);
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)("auth"),
    __metadata("design:paramtypes", [auth_service_1.AuthService,
        user_service_1.UserService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map