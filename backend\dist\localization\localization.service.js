"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalizationService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const utils_1 = require("../utils");
const defaultLocale = "en";
let LocalizationService = class LocalizationService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    getFirstNonNullValue(localizations, locales) {
        const localeLocalizationMap = new Map(localizations.map((localization) => [
            localization.locale,
            localization.value,
        ]));
        for (const locale of locales) {
            if (localeLocalizationMap.has(locale)) {
                return localeLocalizationMap.get(locale);
            }
        }
        if (localeLocalizationMap.has(defaultLocale)) {
            return localeLocalizationMap.get(defaultLocale);
        }
        return localizations[0]?.value ?? null;
    }
    async getOne(id) {
        return await this.prisma.localization.findUnique({
            where: { id, deletedAt: null },
        });
    }
    async getMany(where, pagination) {
        return await this.prisma.localization.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
        });
    }
    async createOne(data) {
        return await this.prisma.localization.create({ data });
    }
    async createMany(data) {
        return await this.prisma.localization.createMany({ data });
    }
    async updateOne(id, data) {
        return await this.prisma.localization.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }
    async updateMany(where, data) {
        return await this.prisma.localization.updateMany({ where, data });
    }
    async softDeleteOne(id) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }
    async softDeleteMany(where) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }
    async deleteOne(id) {
        return await this.prisma.localization.delete({ where: { id } });
    }
    async deleteMany(where) {
        return await this.prisma.localization.deleteMany({ where });
    }
};
exports.LocalizationService = LocalizationService;
exports.LocalizationService = LocalizationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], LocalizationService);
//# sourceMappingURL=localization.service.js.map