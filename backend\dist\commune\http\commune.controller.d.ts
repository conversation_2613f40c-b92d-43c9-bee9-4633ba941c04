import { CurrentUser } from "src/auth/types";
import { CommuneService } from "../commune.service";
import { CommuneMemberService } from "../commune-member.service";
import { UserService } from "src/user/user.service";
import { Common, Commune } from "@commune/api";
export declare class CommuneController {
    private readonly communeService;
    private readonly communeMemberService;
    private readonly userService;
    constructor(communeService: CommuneService, communeMemberService: CommuneMemberService, userService: UserService);
    getInvitations(user: CurrentUser, pagination: Common.Pagination): Promise<{
        status: "pending" | "accepted" | "rejected" | "expired";
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        communeId: string;
    }[]>;
    getInvitationsForCommune(id: string, pagination: Common.Pagination): Promise<{
        status: "pending" | "accepted" | "rejected" | "expired";
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        communeId: string;
    }[]>;
    createInvitation(body: Commune.CreateCommuneInvitationRequest, user: CurrentUser): Promise<{
        id: string;
    }>;
    deleteInvitation(invitationId: string, user: CurrentUser): Promise<void>;
    acceptInvitation(invitationId: string, user: CurrentUser): Promise<void>;
    rejectInvitation(invitationId: string, user: CurrentUser): Promise<void>;
    getJoinRequests(user: CurrentUser, pagination: Common.Pagination): Promise<{
        status: "pending" | "accepted" | "rejected";
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        communeId: string;
    }[]>;
    getJoinRequestsForCommune(id: string, pagination: Common.Pagination): Promise<{
        status: "pending" | "accepted" | "rejected";
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        communeId: string;
    }[]>;
    createJoinRequest(body: Commune.CreateCommuneJoinRequestRequest, user: CurrentUser): Promise<{
        id: string;
    }>;
    deleteJoinRequest(joinRequestId: string, user: CurrentUser): Promise<void>;
    acceptJoinRequest(joinRequestId: string, user: CurrentUser): Promise<void>;
    rejectJoinRequest(joinRequestId: string, user: CurrentUser): Promise<void>;
    private getCommuneToReturn;
    getCommunes(pagination: Common.Pagination): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
        headMember: {
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            actorType: "user";
            actorId: string;
        };
        memberCount: number;
    }[]>;
    createCommune(body: Commune.CreateCommuneRequest, user: CurrentUser, files?: Array<Express.Multer.File>): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
        headMember: {
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            actorType: "user";
            actorId: string;
        };
        memberCount: number;
    }>;
    uploadCommuneImages(id: string, user: CurrentUser, files: Array<Express.Multer.File>): Promise<{
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[]>;
    getCommune(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
        headMember: {
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            actorType: "user";
            actorId: string;
        };
        memberCount: number;
    }>;
    updateCommune(id: string, body: Commune.UpdateCommuneRequest, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
        headMember: {
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            actorType: "user";
            actorId: string;
        };
        memberCount: number;
    }>;
    deleteCommune(id: string, user: CurrentUser): Promise<void>;
    getCommuneMembers(id: string, pagination: Common.Pagination): Promise<{
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
        actorType: "user";
        actorId: string;
        joinedAt: Date;
        leftAt: Date | null;
    }[]>;
    getCommuneMember(memberId: string): Promise<{
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
        actorType: "user";
        actorId: string;
        joinedAt: Date;
        leftAt: Date | null;
    }>;
    createCommuneMember(id: string, body: Commune.CreateCommuneMemberRequest): Promise<{
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
        actorType: "user";
        actorId: string;
        joinedAt: Date;
        leftAt: Date | null;
    }>;
    deleteCommuneMember(memberId: string): Promise<boolean>;
    transferHeadStatus(id: string, body: Commune.TransferHeadStatusRequest, user: CurrentUser): Promise<void>;
}
