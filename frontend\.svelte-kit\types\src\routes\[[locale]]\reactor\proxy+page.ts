// @ts-nocheck
import type { PageLoad } from "./$types";
import type { PostEntity } from "./[id]/types";

import { fixResponseJsonDatesForArray, handleUnauthorized } from "$lib";

export const load = async ({ fetch, url }: Parameters<PageLoad>[0]) => {
  const response = await fetch("/api/reactor/post?page=1&size=20");

  handleUnauthorized(response, url);

  if (!response.ok) {
    return {
      posts: [],
      isHasMorePosts: false,
    };
  }

  const data = await response.json() as {
    items: PostEntity[];
    total: number;
  };

  // Convert date strings to Date objects
  const posts: PostEntity[] = fixResponseJsonDatesForArray(data.items);

  return {
    posts,
    isHasMorePosts: data.items.length === 20, // If we got a full page, there might be more
  };
};
