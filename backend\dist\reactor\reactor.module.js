"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorModule = void 0;
const common_1 = require("@nestjs/common");
const reactor_service_1 = require("./reactor.service");
const reactor_controller_1 = require("./http/reactor.controller");
const lens_service_1 = require("./lens/lens.service");
const rating_module_1 = require("../rating/rating.module");
const minio_module_1 = require("../minio/minio.module");
let ReactorModule = class ReactorModule {
};
exports.ReactorModule = ReactorModule;
exports.ReactorModule = ReactorModule = __decorate([
    (0, common_1.Module)({
        imports: [rating_module_1.RatingModule, minio_module_1.MinioModule],
        controllers: [reactor_controller_1.ReactorController],
        providers: [reactor_service_1.ReactorService, lens_service_1.ReactorLensService],
        exports: [reactor_service_1.ReactorService],
    })
], ReactorModule);
//# sourceMappingURL=reactor.module.js.map