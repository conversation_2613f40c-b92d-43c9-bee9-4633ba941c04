import { Prisma } from "@prisma/client";
import { CurrentUser } from "src/auth/types";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";
type CreateDto = {
    userId: string;
    votingId: string;
    optionId: string;
};
export declare class VoteService extends BaseService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    canGet(id: string, user: CurrentUser): Promise<true>;
    check(ids: string[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string | null;
        optionId: string | null;
        actorType: import("@prisma/client").$Enums.VoteActorType;
        actorId: string;
    }[]>;
    getOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string | null;
        optionId: string | null;
        actorType: import("@prisma/client").$Enums.VoteActorType;
        actorId: string;
    } | null>;
    getOneOrThrow(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string | null;
        optionId: string | null;
        actorType: import("@prisma/client").$Enums.VoteActorType;
        actorId: string;
    }>;
    getMany(where: Prisma.VoteWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string | null;
        optionId: string | null;
        actorType: import("@prisma/client").$Enums.VoteActorType;
        actorId: string;
    }[]>;
    createOne(data: Prisma.VoteCreateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string | null;
        optionId: string | null;
        actorType: import("@prisma/client").$Enums.VoteActorType;
        actorId: string;
    }>;
    createMany(data: Prisma.VoteCreateManyInput[]): Promise<Prisma.BatchPayload>;
    create(data: CreateDto): Promise<void>;
    updateOne(id: string, data: Prisma.VoteUpdateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string | null;
        optionId: string | null;
        actorType: import("@prisma/client").$Enums.VoteActorType;
        actorId: string;
    }>;
    updateMany(where: Prisma.VoteWhereInput, data: Prisma.VoteUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string | null;
        optionId: string | null;
        actorType: import("@prisma/client").$Enums.VoteActorType;
        actorId: string;
    }>;
    softDeleteMany(where: Prisma.VoteWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string | null;
        optionId: string | null;
        actorType: import("@prisma/client").$Enums.VoteActorType;
        actorId: string;
    }>;
    deleteMany(where: Prisma.VoteWhereInput): Promise<Prisma.BatchPayload>;
}
export {};
