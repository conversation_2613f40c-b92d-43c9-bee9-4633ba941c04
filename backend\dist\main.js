"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const express_session_1 = __importDefault(require("express-session"));
const session_file_store_1 = __importDefault(require("session-file-store"));
const SESSION_TTL = 86400 * 30;
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const FileStoreSession = (0, session_file_store_1.default)(express_session_1.default);
    app.use((0, cookie_parser_1.default)());
    app.use((0, express_session_1.default)({
        store: new FileStoreSession({
            path: "./.sessions",
            ttl: SESSION_TTL,
            retries: 5,
            factor: 1,
            minTimeout: 50,
            maxTimeout: 86400000,
        }),
        secret: process.env.SESSION_SECRET ||
            (() => {
                console.error("SESSION_SECRET is not set, using dev default value");
                return "your-secret-key-change-in-production";
            })(),
        name: "session",
        resave: false,
        saveUninitialized: false,
        cookie: {
            secure: process.env.NODE_ENV === "production",
            httpOnly: true,
            maxAge: SESSION_TTL * 1000,
            sameSite: "strict",
        },
    }));
    app.enableCors({
        credentials: true,
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
    });
    await app.listen(4000);
}
bootstrap();
//# sourceMappingURL=main.js.map