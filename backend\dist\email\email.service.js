"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const zod_1 = require("zod");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const nodemailer_1 = require("nodemailer");
let EmailService = class EmailService {
    constructor(configService) {
        this.configService = configService;
        this.isIgnoreEmailErrors = Boolean(this.configService.get("IGNORE_EMAIL_ERRORS"));
        this.isDisableOtpEmail = Boolean(this.configService.get("DISABLE_OTP_EMAIL"));
        const host = zod_1.z
            .string()
            .nonempty()
            .parse(this.configService.get("EMAIL_HOST"));
        const port = zod_1.z.coerce
            .number()
            .int()
            .positive()
            .parse(this.configService.get("EMAIL_PORT"));
        const user = zod_1.z
            .string()
            .nonempty()
            .parse(this.configService.get("EMAIL_USER"));
        const pass = zod_1.z
            .string()
            .nonempty()
            .parse(this.configService.get("EMAIL_PASSWORD"));
        const rawRejectUnauthorized = this.configService.get("EMAIL_REJECT_UNAUTHORIZED");
        const rejectUnauthorized = rawRejectUnauthorized === undefined
            ? undefined
            : Boolean(rawRejectUnauthorized);
        const options = {
            host,
            secure: false,
            port,
            auth: {
                user,
                pass,
            },
            tls: {
                rejectUnauthorized,
            },
        };
        this.transporter = (0, nodemailer_1.createTransport)(options);
    }
    joinAddress(sender, domain) {
        return `${sender}@${domain}`;
    }
    async send(dto) {
        try {
            console.dir({ dto }, { depth: null });
            if (this.isDisableOtpEmail) {
                console.debug("EmailService.send.otpEmailDisabled");
                return false;
            }
            await this.transporter.verify();
            await this.transporter.sendMail({
                from: dto.from,
                to: dto.to,
                subject: dto.subject,
                text: dto.text,
            });
            return true;
        }
        catch (e) {
            if (!this.isIgnoreEmailErrors) {
                throw e;
            }
            return false;
        }
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], EmailService);
//# sourceMappingURL=email.service.js.map