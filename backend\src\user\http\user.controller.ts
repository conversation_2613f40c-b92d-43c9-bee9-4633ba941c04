import {
    Body,
    Controller,
    Get,
    NotFoundException,
    Param,
    Post,
    Put,
    Query,
    UploadedFile,
    UseGuards,
    UseInterceptors,
    ParseFilePipe,
    MaxFileSizeValidator,
    FileTypeValidator,
    BadRequestException,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import { z, ZodPipe } from "src/zod";
import { getError } from "src/common/errors";
import { UserService } from "../user.service";
import { UserTitleService } from "../user-title.service";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { CurrentUser } from "src/auth/types";
import { HttpSessionAuthGuard } from "src/auth/http/session-auth.guard";

import { Common, User } from "@commune/api";

// Constants for file upload validation
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];

@Controller("user")
@UseGuards(HttpSessionAuthGuard)
export class UserController {
    constructor(
        private readonly userService: UserService,
        private readonly userTitleService: UserTitleService,
    ) {}

    @Get()
    async getUsers(
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
    ) {
        const users = await this.userService.getMany({}, pagination);

        return Common.parseInput(User.UsersSchema, users);
    }

    @Get(":id")
    async getUser(@Param("id", new ZodPipe(Common.id)) id: string) {
        const user = await this.userService.getOne(id);

        if (!user) {
            throw new NotFoundException(...getError("user_not_found"));
        }

        return Common.parseInput(User.UserSchema, user);
    }

    @Put(":id")
    @UseGuards(HttpSessionAuthGuard)
    async updateUser(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(User.UpdateUserRequestSchema))
        body: User.UpdateUserRequest,
        @HttpCurrentUser() currentUser: CurrentUser,
    ) {
        const user = await this.userService.update(id, body, currentUser);

        return Common.parseInput(User.UserSchema, user);
    }

    @Post(":id/image")
    @UseGuards(HttpSessionAuthGuard)
    @UseInterceptors(FileInterceptor("image"))
    async uploadUserImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() currentUser: CurrentUser,
        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
                    new FileTypeValidator({
                        fileType: ALLOWED_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        file: Express.Multer.File,
    ) {
        try {
            // Check permissions
            await this.userService.canChange(id, currentUser);

            if (!file) {
                throw new BadRequestException("No file uploaded");
            }

            const image = await this.userService.uploadUserImage(id, file);

            return Common.parseInput(Common.ImageSchema, image);
        } catch (error) {
            console.error("Error uploading image:", error);

            if (error instanceof BadRequestException) {
                throw error;
            }

            throw new BadRequestException("Failed to upload image");
        }
    }

    @Get(":id/title")
    async getUserTitles(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Query("active", new ZodPipe(z.boolean())) active: boolean,
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
    ) {
        const userTitles = await this.userTitleService.getMany(
            {
                ownerId: id,
                isActive: active,
            },
            pagination,
        );

        return Common.parseInput(User.UserTitlesSchema, userTitles);
    }

    @Put(":id/title/:titleId")
    async updateUserTitle(
        @Param("titleId", new ZodPipe(Common.id)) titleId: string,
        @Body(new ZodPipe(User.UpdateUserTitleRequestSchema))
        body: User.UpdateUserTitleRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const userTitle = await this.userTitleService.update(
            titleId,
            body,
            user,
        );

        return Common.parseInput(User.UserTitleSchema, userTitle);
    }

    @Get(":id/note")
    async getUserNote(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const note = await this.userService.getUserNote(id, user);

        return Common.parseInput(User.GetUserNoteResponseSchema, {
            text: note?.text ?? null,
        });
    }

    @Put(":id/note")
    async setUserNote(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(User.SetUserNoteRequestSchema))
        body: User.SetUserNoteRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.userService.setUserNote(
            {
                userId: id,
                text: body.text,
            },
            user,
        );

        return true;
    }
}
