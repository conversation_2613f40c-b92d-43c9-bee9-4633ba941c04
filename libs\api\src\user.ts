import type { Infer } from "./types";

import { z } from "zod";
import { email, id, ImagesSchema, LocalizationsSchema } from "./common";

export const userName = LocalizationsSchema;
export const userDescription = LocalizationsSchema;

export const userTitleIsActive = z.boolean();
export const userTitleColor = z.string().nonempty().nullable();

export const userNoteText = z.string().nonempty();

export type UserRole = Infer<typeof UserRoleSchema>;
export const UserRoleSchema = z.enum([
    "admin",
    "moderator",
    "user",
]);

export type Author = Infer<typeof AuthorSchema>;
export const AuthorSchema = z.object({
    id,
    email,
    name: userName,
    images: ImagesSchema,
})

export type User = Infer<typeof UserSchema>;
export const UserSchema = z.object({
    id,
    email,
    role: UserRoleSchema,

    name: userName,
    description: userDescription,

    images: ImagesSchema,

    createdAt: z.date(),
    updatedAt: z.date(),
});

export type Users = Infer<typeof UsersSchema>;
export const UsersSchema = z.array(UserSchema);

export type UpdateUserRequest = Infer<typeof UpdateUserRequestSchema>;
export const UpdateUserRequestSchema = z
    .object({
        name: userName,
        description: userDescription,
    })
    .partial();

export type UserTitle = Infer<typeof UserTitleSchema>;
export const UserTitleSchema = z.object({
    id,
    ownerId: id.nullable(),

    isActive: userTitleIsActive,
    color: userTitleColor,

    createdAt: z.date(),
    updatedAt: z.date(),
});

export type UserTitles = Infer<typeof UserTitlesSchema>;
export const UserTitlesSchema = z.array(UserTitleSchema);

export type UpdateUserTitleRequest = Infer<typeof UpdateUserTitleRequestSchema>;
export const UpdateUserTitleRequestSchema = z
    .object({
        isActive: userTitleIsActive,
        color: userTitleColor,
    })
    .partial();

export type GetUserNoteResponse = Infer<typeof GetUserNoteResponseSchema>;
export const GetUserNoteResponseSchema = z.object({
    text: userNoteText.nullable(),
});

export type SetUserNoteRequest = Infer<typeof SetUserNoteRequestSchema>;
export const SetUserNoteRequestSchema = z.object({
    text: userNoteText.nullable(),
});
