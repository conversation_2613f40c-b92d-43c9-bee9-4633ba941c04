// commune
model Commune {
    @@map("communes")

    id String @id @default(nanoid())

    images Image[] @relation("commune_images")
    tags   Tag[]   @relation("commune_tags")

    name        Localization[] @relation("commune_name")
    description Localization[] @relation("commune_description")

    members CommuneMember[] @relation("commune_members")

    invitations  CommuneInvitation[]  @relation("commune_invitations")
    joinRequests CommuneJoinRequest[] @relation("commune_join_requests")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

// commune-member-type
enum CommuneMemberType {
    @@map("commune_member_type")

    // commune
    user
}

// commune-member
model CommuneMember {
    @@map("commune_members")

    id String @id @default(nanoid())

    communeId String  @map("commune_id")
    commune   Commune @relation("commune_members", fields: [communeId], references: [id])

    actorType CommuneMemberType
    actorId   String @map("actor_id")

    isHead <PERSON> @map("is_head") @default(false)

    joinedAt DateTime  @map("joined_at") @db.Timestamptz(3) @default(now())
    leftAt   DateTime? @map("left_at")   @db.Timestamptz(3)

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

// commune-invitation-status
enum CommuneInvitationStatus {
    @@map("commune_invitation_status")

    pending
    accepted
    rejected
    expired
}

// commune-invitation
model CommuneInvitation {
    @@map("commune_invitations")

    id String @id @default(nanoid())

    communeId String  @map("commune_id")
    commune   Commune @relation("commune_invitations", fields: [communeId], references: [id])

    userId String @map("user_id")
    user   User   @relation("commune_invitations", fields: [userId], references: [id])

    expiresAt DateTime @map("expires_at") @db.Timestamptz(3)

    status CommuneInvitationStatus @default(pending)

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

// commune-join-request-status
enum CommuneJoinRequestStatus {
    @@map("commune_join_request_status")

    pending
    accepted
    rejected
}

// commune-join-request
model CommuneJoinRequest {
    @@map("commune_join_requests")

    id String @id @default(nanoid())

    communeId String  @map("commune_id")
    commune   Commune @relation("commune_join_requests", fields: [communeId], references: [id])

    userId String @map("user_id")
    user   User   @relation("commune_join_requests", fields: [userId], references: [id])

    status CommuneJoinRequestStatus @default(pending)

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}
