import type { Auth, Commune } from "@commune/api";
import type { PageLoad } from "./$types";

export const load: PageLoad = async ({ fetch, params, parent }) => {
  const [
    user,
    commune,
    members,
  ] = await Promise.all([
    fetch("/api/auth/me").then<Auth.GetMeResponse>(r => r.json()),
    fetch(`/api/commune/${params.id}`).then<Commune.Commune>(r => r.json()),
    fetch(`/api/commune/${params.id}/member`).then<Commune.CommuneMember[]>(r => r.json()),
  ]);

  // Determine user permissions and status
  const isLoggedIn = !!user;
  const isAdmin = user?.role === "admin";
  const isHeadMember = isLoggedIn && commune.headMember.actorType === "user" && commune.headMember.actorId === user.id;
  const isMember = isLoggedIn && members.some(member =>
    member.actorType === "user" &&
    member.actorId === user.id &&
    !member.leftAt
  );

  // Check if user has pending join request (only if logged in and not a member)
  let hasPendingJoinRequest = false;
  if (isLoggedIn && !isMember) {
    try {
      const joinRequestsResponse = await fetch("/api/commune/join-request");
      if (joinRequestsResponse.ok) {
        const joinRequests = await joinRequestsResponse.json();
        hasPendingJoinRequest = joinRequests.some((request: any) =>
          request.communeId === params.id &&
          request.status === "pending"
        );
      }
    } catch (error) {
      console.error("Failed to check join requests:", error);
    }
  }

  return {
    commune,
    members,
    userPermissions: {
      isLoggedIn,
      isAdmin,
      isHeadMember,
      isMember,
      canInvite: isAdmin || isHeadMember,
      canRequestJoin: isLoggedIn && !isMember && !hasPendingJoinRequest,
      hasPendingJoinRequest,
    },
  };
};
