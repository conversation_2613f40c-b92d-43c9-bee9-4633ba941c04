import { UserService } from "../user.service";
import { UserTitleService } from "../user-title.service";
import { CurrentUser } from "src/auth/types";
import { Common, User } from "@commune/api";
export declare class UserController {
    private readonly userService;
    private readonly userTitleService;
    constructor(userService: UserService, userTitleService: UserTitleService);
    getUsers(pagination: Common.Pagination): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
        role: "admin" | "moderator" | "user";
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[]>;
    getUser(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
        role: "admin" | "moderator" | "user";
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
    }>;
    updateUser(id: string, body: User.UpdateUserRequest, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
        role: "admin" | "moderator" | "user";
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
    }>;
    uploadUserImage(id: string, currentUser: CurrentUser, file: Express.Multer.File): Promise<{
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    getUserTitles(id: string, active: boolean, pagination: Common.Pagination): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        ownerId: string | null;
        isActive: boolean;
        color: string | null;
    }[]>;
    updateUserTitle(titleId: string, body: User.UpdateUserTitleRequest, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        ownerId: string | null;
        isActive: boolean;
        color: string | null;
    }>;
    getUserNote(id: string, user: CurrentUser): Promise<{
        text: string | null;
    }>;
    setUserNote(id: string, body: User.SetUserNoteRequest, user: CurrentUser): Promise<boolean>;
}
