import type { PageLoad } from "./$types";
import type { User, Rating } from "@commune/api";

import { error } from "@sveltejs/kit";
import { fixResponseJsonDates, handleUnauthorized } from "$lib";

export const load: PageLoad = async ({ fetch, params, url }) => {
  const [
    userResponse,
    feedbacksResponse,
    currentUserResponse,
  ] = await Promise.all([
    fetch(`/api/user/${params.id}`),
    fetch(`/api/rating/${params.id}/feedback?page=1&size=20`),
    fetch("/api/auth/me"),
  ])

  handleUnauthorized(userResponse, url);
  handleUnauthorized(feedbacksResponse, url);
  handleUnauthorized(currentUserResponse, url);

  if (!userResponse.ok) {
    throw error(500, {
      message: `Failed to fetch user: ${userResponse.statusText}`,
    });
  }

  if (!feedbacksResponse.ok) {
    throw error(500, {
      message: `Failed to fetch feedbacks: ${feedbacksResponse.statusText}`,
    });
  }

  if (!currentUserResponse.ok) {
    throw error(500, {
      message: `Failed to fetch current user: ${currentUserResponse.statusText}`,
    });
  }

  const user: User.User = await userResponse.json().then(fixResponseJsonDates);
  const feedbacks: Rating.GetUserFeedbacksResponse = await feedbacksResponse.json();
  const currentUser = await currentUserResponse.json();

  return {
    user,
    feedbacks,
    currentUser,
    isHasMoreFeedbacks: feedbacks.length === 20, // If we got a full page, there might be more
  };
};
