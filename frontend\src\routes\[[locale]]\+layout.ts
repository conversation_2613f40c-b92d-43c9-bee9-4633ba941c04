import type { LayoutLoad, LayoutLoadEvent } from "./$types";

import { Common } from "@commune/api";
import { getAppropriateLocalizationFactory, getCurrentUser } from "$lib";

export const load: LayoutLoad = (event) => {
  const routeLocale = getRouteLocale(event);

  const hrefLocale = routeLocale ? `/${routeLocale}` : "";

  return {
    routeLocale,
    preferredLocale: event.data.preferredLocale,
    locale: routeLocale ?? event.data.preferredLocale ?? "en",
    user: getCurrentUser(),
    toLocaleHref(href: string) {
      return `${hrefLocale}${href}`;
    },
    getAppropriateLocalization: getAppropriateLocalizationFactory(
      routeLocale,
      event.data.userLocales,
    ),
  };
};

function getRouteLocale(event: LayoutLoadEvent) {
  const parsedLocale = Common.LocalizationLocaleSchema.safeParse(event.params.locale);

  if (parsedLocale.success) {
    return parsedLocale.data;
  }

  return null;
}
