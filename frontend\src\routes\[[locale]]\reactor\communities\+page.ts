import type { PageLoad } from "./$types";
import type { Auth, Reactor } from "@commune/api";

import { handleUnauthorized } from "$lib";

const PAGE_SIZE = 20;

async function fetchCommunities(fetch: typeof globalThis.fetch, searchQuery: string, page: number = 1): Promise<Reactor.GetCommunitiesOutput> {
  const searchBody = searchQuery.trim() ? { query: searchQuery.trim() } : {};
  
  const response = await fetch(
    `/api/reactor/community?page=${page}&size=${PAGE_SIZE}&body=${encodeURIComponent(
      JSON.stringify(searchBody),
    )}`,
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch communities: ${response.statusText}`);
  }

  return response.json();
}

async function fetchCurrentUser(fetch: typeof globalThis.fetch): Promise<Auth.GetMeResponse | null> {
  try {
    const response = await fetch("/api/auth/me");
    return response.ok ? await response.json() : null;
  } catch {
    return null;
  }
}

export const load: PageLoad = async ({ fetch, url }) => {
  const searchQuery = url.searchParams.get("search") || "";

  const [user, communities] = await Promise.all([
    fetchCurrentUser(fetch),
    fetchCommunities(fetch, searchQuery).catch(() => []), // Fallback to empty array on error
  ]);

  return {
    communities,
    searchQuery,
    isHasMoreCommunities: communities.length === PAGE_SIZE,
    user,
    pageSize: PAGE_SIZE,
  };
};
