import type { Tag } from "@commune/api";
import type { CurrentUser } from "src/auth/types";

import { ForbiddenException, Injectable, Logger } from "@nestjs/common";
import { toPrismaLocalizations, toPrismaLocalizationsWhere } from "src/utils";
import { PrismaService } from "src/prisma/prisma.service";
import { getError } from "src/common/errors";

@Injectable()
export class TagService {
    private readonly logger = new Logger(TagService.name);

    constructor(private readonly prisma: PrismaService) {}

    async getTags(input: Tag.GetTagsInput, user: CurrentUser) {
        const { ids, query } = input;

        const tags = await this.prisma.tag.findMany({
            where: Object.assign(
                {},
                ids && { id: { in: ids } },
                query && { name: toPrismaLocalizationsWhere(query) },
                !user.isAdmin && { deletedAt: null },
            ),
            select: {
                id: true,
                name: {
                    select: {
                        locale: true,
                        value: true,
                    },
                },
                deletedAt: user.isAdmin,
            },
        });

        return tags;
    }

    async createTag(input: Tag.CreateTagInput, user: CurrentUser) {
        if (!user.isAdmin) {
            throw new ForbiddenException(...getError("must_be_admin"));
        }

        const tag = await this.prisma.tag.create({
            data: {
                name: {
                    create: toPrismaLocalizations(input.name, "name"),
                },
            },
        });

        return tag;
    }

    async updateTag(id: string, input: Tag.UpdateTagInput, user: CurrentUser) {
        if (!user.isAdmin) {
            throw new ForbiddenException(...getError("must_be_admin"));
        }

        const tag = await this.prisma.tag.update({
            where: { id },
            data: {
                name: {
                    deleteMany: {},
                    create: toPrismaLocalizations(input.name, "name"),
                },
            },
        });

        return tag;
    }

    async deleteTag(id: string, user: CurrentUser) {
        if (!user.isAdmin) {
            throw new ForbiddenException(...getError("must_be_admin"));
        }

        const tag = await this.prisma.tag.update({
            where: { id },
            data: {
                deletedAt: new Date(),
            },
        });

        return tag;
    }
}
