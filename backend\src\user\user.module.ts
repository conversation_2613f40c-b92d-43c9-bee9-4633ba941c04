import { Module } from "@nestjs/common";
import { UserService } from "./user.service";
import { UserController } from "./http/user.controller";
import { UserTitleService } from "./user-title.service";
import { MinioModule } from "src/minio/minio.module";

@Module({
    imports: [MinioModule],
    controllers: [UserController],
    providers: [UserService, UserTitleService],
    exports: [UserService, UserTitleService],
})
export class UserModule {}
