// @ts-nocheck
import type { Common, Auth, Commune } from "@commune/api";
import type { PageLoad } from "./$types";

import { handleUnauthorized } from "$lib";

export interface CommuneJoinRequest {
  id: string;
  communeId: string;
  userId: string;
  status: Commune.CommuneJoinRequestStatus;
  createdAt: string;
  updatedAt: string;
}

export interface CommuneJoinRequestWithUserDetails extends CommuneJoinRequest {
  user: {
    id: string;
    email: string;
    name: Common.Localizations;
    description: Common.Localizations;
    images?: {
      id: string;
      url: string;
      source: string;
    }[];
    createdAt: string;
    updatedAt: string;
  };
}

export const load = async ({ fetch, params, url }: Parameters<PageLoad>[0]) => {
  const [
    user,
    communeResponse
  ] = await Promise.all([
    fetch("/api/auth/me").then<Auth.GetMeResponse>(r => r.json()),
    fetch(`/api/commune/${params.id}`),
  ]);
  
  handleUnauthorized(communeResponse, url);
  
  if (!communeResponse.ok) {
    throw new Error(`Failed to fetch commune: ${communeResponse.statusText}`);
  }
  
  const commune: Commune.Commune = await communeResponse.json();
  
  // Check if user has permission to view join requests (admin or head member)
  const isAdmin = user?.role === "admin";
  const isHeadMember = user && commune.headMember.actorType === "user" && commune.headMember.actorId === user.id;
  
  if (!isAdmin && !isHeadMember) {
    throw new Error("Access denied: You must be an admin or commune head to view join requests");
  }

  // Fetch join requests for this commune
  const joinRequestsResponse = await fetch(`/api/commune/${params.id}/join-request?page=1&size=20`);
  handleUnauthorized(joinRequestsResponse, url);

  const joinRequests: CommuneJoinRequest[] = joinRequestsResponse.ok ? await joinRequestsResponse.json() : [];

  // Fetch user details for each join request
  const joinRequestsWithUserDetails: (CommuneJoinRequestWithUserDetails | null)[] = await Promise.all(
    joinRequests.map(async (joinRequest) => {
      try {
        const userResponse = await fetch(`/api/user/${joinRequest.userId}`);
        if (userResponse.ok) {
          const userData = await userResponse.json();
          return {
            ...joinRequest,
            user: userData,
          } as CommuneJoinRequestWithUserDetails;
        }
        // If user fetch fails, we'll handle this in the component
        console.warn(`Failed to fetch user ${joinRequest.userId}: ${userResponse.statusText}`);
        return null;
      } catch (error) {
        console.error(`Failed to fetch user ${joinRequest.userId}:`, error);
        return null;
      }
    })
  );

  // Filter out failed fetches
  const validJoinRequests = joinRequestsWithUserDetails.filter(
    (joinRequest): joinRequest is CommuneJoinRequestWithUserDetails => joinRequest !== null
  );

  return {
    commune,
    joinRequests: validJoinRequests,
    isHasMoreJoinRequests: joinRequests.length === 20, // If we got a full page, there might be more
    userPermissions: {
      isAdmin,
      isHeadMember,
      canManageJoinRequests: isAdmin || isHeadMember,
    },
  };
};
