import type { Common } from "@commune/api";

import { match } from "@formatjs/intl-localematcher";

export const DEFAULT_LOCALE = "en";

export type GetAppropriateLocalization = ReturnType<typeof getAppropriateLocalizationFactory>;

export function getAppropriateLocalizationFactory(
  routeLocale: Common.LocalizationLocale | null,
  userLocales: readonly string[],
) {
  const requestedLocales = routeLocale ? [routeLocale, ...userLocales] : userLocales;

  function getAppropriateLocalization(
    localizations: Common.Localizations,
  ) {
    const availableLocales = localizations.map((localization) => localization.locale);

    try {
      const matchedLocale = match(requestedLocales, availableLocales, DEFAULT_LOCALE);
  
      const matchedLocalization = localizations.find(
        (localization) => localization.locale === matchedLocale,
      );
  
      return matchedLocalization?.value ?? localizations[0]?.value ?? null;
    } catch (error) {
      console.error("Error during locale negotiation:", error);
  
      return null;
    }
  }

  return getAppropriateLocalization;
}
