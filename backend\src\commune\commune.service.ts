import { ForbiddenException, Injectable } from "@nestjs/common";
import {
    CommuneInvitationStatus,
    CommuneJoinRequestStatus,
    CommuneMember,
    CommuneMemberType,
    Prisma,
    UserRole,
} from "@prisma/client";
import { toPrismaPagination } from "src/utils";
import { getError } from "src/common/errors";
import { BaseService } from "src/common/base-service";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { Common } from "@commune/api";

const WEEK = 7 * 24 * 60 * 60 * 1000;
const INVITATION_EXPIRATION_TIME = WEEK;

@Injectable()
export class CommuneService extends BaseService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly minioService: MinioService,
    ) {
        super("commune");
    }

    async canGet(id: string, user: CurrentUser): Promise<true> {
        await this.getOneOrThrow(id);

        return true;
    }

    override async canChange(id: string, user: CurrentUser): Promise<true> {
        await this.getOneOrThrow(id);

        const headMember = await this.getHeadMember(id);

        if (user.role !== UserRole.admin) {
            if (
                headMember.actorType === CommuneMemberType.user &&
                headMember.actorId !== user.id
            ) {
                throw new ForbiddenException(
                    ...getError("must_be_head_member"),
                );
            }
        }

        return true;
    }

    async check(ids: string[]) {
        return await this._check(ids, this.prisma.commune, this.entityType);
    }

    isHeadMember(commune: { members: CommuneMember[] }, userId: string) {
        return commune.members.some(
            (member) =>
                member.isHead &&
                member.actorId === userId &&
                member.actorType === CommuneMemberType.user,
        );
    }

    isMember(commune: { members: CommuneMember[] }, userId: string) {
        return commune.members.some(
            (member) =>
                member.actorId === userId &&
                member.actorType === CommuneMemberType.user,
        );
    }

    async getOne(id: string) {
        return await this.prisma.commune.findUnique({
            where: {
                id,
                deletedAt: null,
            },
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }

    async getOneOrThrow(id: string) {
        const commune = await this.getOne(id);

        if (!commune) {
            throw this.createNotFoundException();
        }

        return commune;
    }

    async getMany(
        where: Prisma.CommuneWhereInput,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.commune.findMany({
            ...toPrismaPagination(pagination),
            where,
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }

    async createOne(data: Prisma.CommuneCreateInput) {
        return await this.prisma.commune.create({
            data,
        });
    }

    async createMany(data: Prisma.CommuneCreateManyInput[]) {
        return await this.prisma.commune.createMany({
            data,
        });
    }

    async create(
        data: {
            headUserId?: string;
            name: Common.Localization[];
            description: Common.Localization[];
        },
        user: CurrentUser,
    ) {
        if (!data.headUserId) {
            data.headUserId = user.id;
        }

        if (data.headUserId !== user.id) {
            if (user.role !== "admin") {
                throw new ForbiddenException(
                    ...getError("head_user_id_mismatch"),
                );
            }
        }

        const commune = await this.prisma.commune.create({
            data: {
                members: {
                    create: {
                        actorType: CommuneMemberType.user,
                        actorId: data.headUserId!,
                        isHead: true,
                    },
                },
                name: {
                    create: data.name.map((item) => ({
                        ...item,
                        key: "name",
                    })),
                },
                description: {
                    create: data.description.map((item) => ({
                        ...item,
                        key: "description",
                    })),
                },
            },
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });

        return commune;
    }

    async uploadCommuneImages(communeId: string, files: FileInfo[]) {
        await this.getOneOrThrow(communeId);

        const images = await Promise.all(
            files.map(async (file, index) => {
                const imageUrl = await this.minioService.uploadCommuneImage(
                    file,
                    communeId,
                    index,
                );

                const image = await this.prisma.image.create({
                    data: {
                        url: imageUrl,
                    },
                });

                return image;
            }),
        );

        await this.prisma.commune.update({
            where: { id: communeId },
            data: {
                images: {
                    connect: images.map((image) => ({ id: image.id })),
                },
            },
        });

        return images;
    }

    async updateOne(id: string, data: Prisma.CommuneUpdateInput) {
        return await this.prisma.commune.update({
            where: { id },
            data,
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }

    async getHeadMember(id: string) {
        return await this.prisma.communeMember.findFirstOrThrow({
            where: {
                communeId: id,
                isHead: true,
                deletedAt: null,
            },
        });
    }

    async update(
        id: string,
        data: Prisma.CommuneUpdateInput,
        user: CurrentUser,
    ) {
        const commune = await this.getOne(id);

        if (!commune) {
            throw this.createNotFoundException();
        }

        if (user.role !== "admin") {
            const headMember = await this.getHeadMember(id);

            if (
                headMember.actorType === CommuneMemberType.user &&
                headMember.actorId !== user.id
            ) {
                throw new ForbiddenException(
                    ...getError("must_be_head_member"),
                );
            }
        }

        return await this.updateOne(id, data);
    }

    async updateMany(
        where: Prisma.CommuneWhereInput,
        data: Prisma.CommuneUpdateInput,
    ) {
        return await this.prisma.commune.updateMany({ where, data });
    }

    async softDeleteOne(id: string) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }

    async softDeleteOneCascade(id: string, user: CurrentUser) {
        const commune = await this.getOneOrThrow(id);

        if (!user.isAdmin && !this.isHeadMember(commune, user.id)) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_head_member"),
            );
        }

        await this.prisma.$transaction(async (trx) => {
            const now = new Date();

            await trx.commune.update({
                where: { id },
                data: { deletedAt: now },
            });

            await trx.communeMember.updateMany({
                where: { communeId: id },
                data: { deletedAt: now },
            });
        });
    }

    async softDeleteMany(where: Prisma.CommuneWhereInput) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }

    async deleteOne(id: string) {
        return await this.prisma.commune.delete({ where: { id } });
    }

    async deleteMany(where: Prisma.CommuneWhereInput) {
        return await this.prisma.commune.deleteMany({ where });
    }

    async getInvitationsForUser(
        userId: string,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.communeInvitation.findMany({
            ...toPrismaPagination(pagination),
            where: {
                userId,
                deletedAt: null,
            },
        });
    }

    async getInvitationsForCommune(
        communeId: string,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.communeInvitation.findMany({
            ...toPrismaPagination(pagination),
            where: {
                communeId,
                deletedAt: null,
            },
        });
    }

    async createInvitation(
        communeId: string,
        userId: string,
        currentUser: CurrentUser,
    ) {
        const commune = await this.getOneOrThrow(communeId);

        if (
            currentUser.role !== "admin" &&
            !this.isHeadMember(commune, currentUser.id)
        ) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_head_member"),
            );
        }

        if (this.isMember(commune, userId)) {
            throw new ForbiddenException(
                ...getError("user_already_in_commune"),
            );
        }

        const existingInvitation =
            await this.prisma.communeInvitation.findFirst({
                where: {
                    communeId,
                    userId,
                    status: CommuneInvitationStatus.pending,
                    deletedAt: null,
                },
            });

        if (existingInvitation) {
            return {
                id: existingInvitation.id,
            };
        }

        const invitation = await this.prisma.communeInvitation.create({
            data: {
                communeId,
                userId,
                expiresAt: new Date(Date.now() + INVITATION_EXPIRATION_TIME),
            },
        });

        return {
            id: invitation.id,
        };
    }

    async deleteInvitation(id: string, currentUser: CurrentUser) {
        const invitation =
            await this.prisma.communeInvitation.findUniqueOrThrow({
                where: { id },
            });

        if (invitation.status !== CommuneInvitationStatus.pending) {
            throw new ForbiddenException(
                ...getError("invitation_must_be_pending"),
            );
        }

        const commune = await this.getOneOrThrow(invitation.communeId);

        if (
            currentUser.role !== "admin" &&
            !this.isHeadMember(commune, currentUser.id)
        ) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_head_member"),
            );
        }

        await this.prisma.communeInvitation.update({
            where: { id },
            data: {
                deletedAt: new Date(),
            },
        });

        return true;
    }

    async acceptInvitation(id: string, currentUser: CurrentUser) {
        const invitation =
            await this.prisma.communeInvitation.findUniqueOrThrow({
                where: { id },
            });

        if (invitation.status !== CommuneInvitationStatus.pending) {
            throw new ForbiddenException(
                ...getError("invitation_must_be_pending"),
            );
        }

        if (!currentUser.isAdmin && invitation.userId !== currentUser.id) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_invitation_target"),
            );
        }

        const commune = await this.getOneOrThrow(invitation.communeId);

        if (this.isMember(commune, currentUser.id)) {
            throw new ForbiddenException(
                ...getError("user_already_in_commune"),
            );
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.create({
                data: {
                    communeId: invitation.communeId,
                    actorType: CommuneMemberType.user,
                    actorId: currentUser.id,
                    isHead: false,
                },
            });

            await trx.communeInvitation.update({
                where: { id },
                data: {
                    status: CommuneInvitationStatus.accepted,
                },
            });
        });

        return true;
    }

    async rejectInvitation(id: string, currentUser: CurrentUser) {
        const invitation =
            await this.prisma.communeInvitation.findUniqueOrThrow({
                where: { id },
            });

        if (invitation.status !== CommuneInvitationStatus.pending) {
            throw new ForbiddenException(
                ...getError("invitation_must_be_pending"),
            );
        }

        if (!currentUser.isAdmin && invitation.userId !== currentUser.id) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_invitation_target"),
            );
        }

        const commune = await this.getOneOrThrow(invitation.communeId);

        if (this.isMember(commune, currentUser.id)) {
            throw new ForbiddenException(
                ...getError("user_already_in_commune"),
            );
        }

        await this.prisma.communeInvitation.update({
            where: { id },
            data: {
                status: CommuneInvitationStatus.rejected,
            },
        });

        return true;
    }

    async getJoinRequestsForUser(
        userId: string,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.communeJoinRequest.findMany({
            ...toPrismaPagination(pagination),
            where: {
                userId,
                deletedAt: null,
            },
        });
    }

    async getJoinRequestsForCommune(
        communeId: string,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.communeJoinRequest.findMany({
            ...toPrismaPagination(pagination),
            where: {
                communeId,
                deletedAt: null,
            },
        });
    }

    async createJoinRequest(
        communeId: string,
        userId: string,
        currentUser: CurrentUser,
    ) {
        const commune = await this.getOneOrThrow(communeId);

        if (!currentUser.isAdmin && currentUser.id !== userId) {
            throw new ForbiddenException(...getError("must_be_admin_or_self"));
        }

        if (this.isMember(commune, userId)) {
            throw new ForbiddenException(
                ...getError("user_already_in_commune"),
            );
        }

        const existingJoinRequest =
            await this.prisma.communeJoinRequest.findFirst({
                where: {
                    communeId,
                    userId,
                    status: CommuneJoinRequestStatus.pending,
                    deletedAt: null,
                },
            });

        if (existingJoinRequest) {
            return {
                id: existingJoinRequest.id,
            };
        }

        const joinRequest = await this.prisma.communeJoinRequest.create({
            data: {
                communeId,
                userId,
            },
        });

        return {
            id: joinRequest.id,
        };
    }

    async deleteJoinRequest(id: string, currentUser: CurrentUser) {
        const joinRequest =
            await this.prisma.communeJoinRequest.findUniqueOrThrow({
                where: { id },
            });

        if (joinRequest.status !== CommuneJoinRequestStatus.pending) {
            throw new ForbiddenException(
                ...getError("join_request_must_be_pending"),
            );
        }

        if (!currentUser.isAdmin && joinRequest.userId !== currentUser.id) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_join_request_target"),
            );
        }

        await this.prisma.communeJoinRequest.update({
            where: { id },
            data: {
                deletedAt: new Date(),
            },
        });

        return true;
    }

    async acceptJoinRequest(id: string, currentUser: CurrentUser) {
        const joinRequest =
            await this.prisma.communeJoinRequest.findUniqueOrThrow({
                where: { id },
            });

        if (joinRequest.status !== CommuneJoinRequestStatus.pending) {
            throw new ForbiddenException(
                ...getError("join_request_must_be_pending"),
            );
        }

        const commune = await this.getOneOrThrow(joinRequest.communeId);

        if (
            !currentUser.isAdmin &&
            !this.isHeadMember(commune, currentUser.id)
        ) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_head_member"),
            );
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.create({
                data: {
                    communeId: joinRequest.communeId,
                    actorType: CommuneMemberType.user,
                    actorId: joinRequest.userId,
                    isHead: false,
                },
            });

            await this.prisma.communeJoinRequest.update({
                where: { id },
                data: {
                    status: CommuneJoinRequestStatus.accepted,
                },
            });
        });

        return true;
    }

    async rejectJoinRequest(id: string, currentUser: CurrentUser) {
        const joinRequest =
            await this.prisma.communeJoinRequest.findUniqueOrThrow({
                where: { id },
            });

        if (joinRequest.status !== CommuneJoinRequestStatus.pending) {
            throw new ForbiddenException(
                ...getError("join_request_must_be_pending"),
            );
        }

        const commune = await this.getOneOrThrow(joinRequest.communeId);

        if (
            !currentUser.isAdmin &&
            !this.isHeadMember(commune, currentUser.id)
        ) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_head_member"),
            );
        }

        await this.prisma.communeJoinRequest.update({
            where: { id },
            data: {
                status: CommuneJoinRequestStatus.rejected,
            },
        });

        return true;
    }

    async transferHeadStatus(
        communeId: string,
        newHeadUserId: string,
        currentUser: CurrentUser,
    ) {
        const commune = await this.getOneOrThrow(communeId);

        if (
            !currentUser.isAdmin &&
            !this.isHeadMember(commune, currentUser.id)
        ) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_head_member"),
            );
        }

        if (!this.isMember(commune, newHeadUserId)) {
            throw new ForbiddenException(...getError("must_be_member"));
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.updateMany({
                where: {
                    communeId,
                },
                data: {
                    isHead: false,
                },
            });

            await trx.communeMember.updateMany({
                where: {
                    communeId,
                    actorType: CommuneMemberType.user,
                    actorId: newHeadUserId,
                },
                data: {
                    isHead: true,
                },
            });
        });

        return true;
    }
}
