<script lang="ts">
  import type { Commune } from "@commune/api";

  import { fetchWithAuth } from "$lib";
  import { formatDate } from "$lib/format-date";
  import { page } from "$app/state";
  import DetailImageCarousel from "./detail-image-carousel.svelte";
  import MemberCard from "./member-card.svelte";
  import AddMemberModal from "./add-member-modal.svelte";
  import EditCommuneModal from "./edit-commune-modal.svelte";
  import InviteUserModal from "./invite-user-modal.svelte";

  const i18n = {
    en: {
      _page: {
        title: "— Commune",
      },
      loading: "Loading commune details...",
      communeNotFound: "Commune not found",
      noDescription: "No description available",
      communeDetails: "Commune Details",
      edit: "Edit",
      delete: "Delete Commune",
      members: "Members",
      member: "member",
      members_plural: "members",
      headMember: "Head Member",
      created: "Created",
      addMember: "Add Member",
      invite: "Invite User",
      manageInvitations: "Manage Invitations",
      manageJoinRequests: "Manage Join Requests",
      requestToJoin: "Request to Join",
      requestPending: "Join Request Pending",
      noMembers: "No members found",
      errorFetchingCommune: "Failed to fetch commune",
      errorFetchingMembers: "Failed to fetch members",
      errorOccurred: "An error occurred while fetching data",
      errorSendingJoinRequest: "Failed to send join request",
      joinRequestSent: "Join request sent successfully!",
      sendingJoinRequest: "Sending request...",
      interestedInJoining: "Interested in joining this commune?",
      joinRequestDescription: "Send a join request to the commune head for approval.",
      joinRequestPendingDescription:
        "Your join request is awaiting approval from the commune head.",
      confirmDelete: "Delete Commune",
      confirmDeleteMessage: "Are you sure you want to delete this commune?",
      deleteWarning:
        "This will permanently delete the commune and all its data, including members, invitations, and join requests. This action cannot be undone.",
      deleting: "Deleting...",
      communeDeleted: "Commune deleted successfully",
      errorDeletingCommune: "Failed to delete commune",
      cancel: "Cancel",
      dateFormatLocale: "en-US",
    },
    ru: {
      _page: {
        title: "— Коммуна",
      },
      loading: "Загрузка данных коммуны...",
      communeNotFound: "Коммуна не найдена",
      noDescription: "Нет описания",
      communeDetails: "Информация о коммуне",
      edit: "Редактировать",
      delete: "Удалить коммуну",
      members: "Участники",
      member: "участник",
      members_plural: "участников",
      headMember: "Глава",
      created: "Создана",
      addMember: "Добавить участника",
      invite: "Пригласить пользователя",
      manageInvitations: "Управление приглашениями",
      manageJoinRequests: "Управление заявками",
      requestToJoin: "Подать заявку",
      requestPending: "Заявка на рассмотрении",
      noMembers: "Участники не найдены",
      errorFetchingCommune: "Не удалось загрузить коммуну",
      errorFetchingMembers: "Не удалось загрузить участников",
      errorOccurred: "Произошла ошибка при загрузке данных",
      errorSendingJoinRequest: "Не удалось отправить заявку",
      joinRequestSent: "Заявка отправлена успешно!",
      sendingJoinRequest: "Отправляем заявку...",
      interestedInJoining: "Хотите присоединиться к этой коммуне?",
      joinRequestDescription: "Отправьте заявку главе коммуны для одобрения.",
      joinRequestPendingDescription: "Ваша заявка ожидает одобрения главы коммуны.",
      confirmDelete: "Удалить коммуну",
      confirmDeleteMessage: "Вы уверены, что хотите удалить эту коммуну?",
      deleteWarning:
        "Это навсегда удалит коммуну и все её данные, включая участников, приглашения и заявки. Это действие нельзя отменить.",
      deleting: "Удаление...",
      communeDeleted: "Коммуна успешно удалена",
      errorDeletingCommune: "Не удалось удалить коммуну",
      cancel: "Отмена",
      dateFormatLocale: "ru-RU",
    },
  };

  const { data } = $props();
  const { locale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const communeId = page.params.id;

  const t = $derived(i18n[locale]);

  const user = $derived(data.user);

  let commune = $state(data.commune);
  let members = $state(data.members);
  let error = $state<string | null>(null);
  let showAddMemberModal = $state(false);
  let showEditModal = $state(false);
  let showInviteModal = $state(false);
  let showDeleteModal = $state(false);
  let isSendingJoinRequest = $state(false);
  let isDeleting = $state(false);

  // Get user permissions from page data
  const userPermissions = $derived(data.userPermissions);

  async function refreshMembers() {
    try {
      const membersResponse = await fetchWithAuth(`/api/commune/${communeId}/member`);

      if (!membersResponse.ok) {
        throw new Error(`${t.errorFetchingMembers}: ${membersResponse.statusText}`);
      }

      const membersData: Commune.CommuneMember[] = await membersResponse.json();

      // Sort members by joinedAt date
      const sortedMembers = membersData
        .filter((member) => !member.leftAt) // Only show active members
        .sort((a, b) => a.joinedAt.toISOString().localeCompare(b.joinedAt.toISOString()));

      members = sortedMembers;
    } catch (err) {
      console.error("Error refreshing members:", err);
    }
  }

  function handleAddMemberClick() {
    showAddMemberModal = true;
  }

  function handleAddMemberModalClose() {
    showAddMemberModal = false;
  }

  function handleMemberAdded() {
    refreshMembers();
  }

  function handleMemberRemoved() {
    refreshMembers();
  }

  function handleHeadTransferred() {
    // Refresh the page to update the head status and permissions
    window.location.reload();
  }

  function handleEditClick() {
    showEditModal = true;
  }

  function handleDeleteClick() {
    showDeleteModal = true;
  }

  function handleEditModalClose() {
    showEditModal = false;
  }

  async function handleCommuneUpdated() {
    try {
      const communeResponse = await fetchWithAuth(`/api/commune/${communeId}`);

      if (!communeResponse.ok) {
        throw new Error(`Failed to fetch updated commune: ${communeResponse.statusText}`);
      }

      const communeData: Commune.Commune = await communeResponse.json();
      commune = communeData;
    } catch (err) {
      console.error("Error refreshing commune data:", err);
    }
  }

  const communeName = $derived(getAppropriateLocalization(commune.name));
  const communeDescription = $derived(getAppropriateLocalization(commune.description));

  const isCurrentUserHead = $derived(
    user
      ? commune.headMember.actorType === "user" && commune.headMember.actorId === user.id
      : false,
  );

  // Invite modal handlers
  function handleInviteClick() {
    showInviteModal = true;
  }

  function handleInviteModalClose() {
    showInviteModal = false;
  }

  function handleInviteSent() {
    // Optionally refresh members or show success message
    console.log("Invitation sent successfully");
  }

  // Join request handler
  async function handleJoinRequest() {
    if (!user) return;

    isSendingJoinRequest = true;
    error = null;

    try {
      const response = await fetchWithAuth("/api/commune/join-request", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          communeId,
          userId: user.id,
        }),
      });

      if (!response.ok) {
        throw new Error(`${t.errorSendingJoinRequest}: ${response.statusText}`);
      }

      // Show success message and update UI
      alert(t.joinRequestSent);

      // Refresh the page to update permissions
      window.location.reload();
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorSendingJoinRequest;
      console.error("Error sending join request:", err);
    } finally {
      isSendingJoinRequest = false;
    }
  }

  async function handleConfirmDelete() {
    isDeleting = true;
    error = null;

    try {
      const response = await fetchWithAuth(`/api/commune/${communeId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error(`${t.errorDeletingCommune}: ${response.statusText}`);
      }

      alert(t.communeDeleted);

      // Redirect to communes list
      window.location.href = toLocaleHref("/communes");
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorDeletingCommune;
      console.error("Error deleting commune:", err);
    } finally {
      isDeleting = false;
    }
  }

  function closeDeleteModal() {
    showDeleteModal = false;
  }
</script>

<svelte:head>
  <title>{communeName} {t._page.title}</title>
</svelte:head>

<div class="container py-4">
  {#if error}
    <div class="alert alert-danger">
      {error}
    </div>
  {:else}
    <div class="row">
      <div class="col-lg-8">
        <!-- Image Carousel -->
        <DetailImageCarousel images={commune.images || []} {locale} />

        <!-- Commune Information -->
        <div class="mb-4">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="mb-0">{communeName}</h2>
          </div>
          <p class="lead text-muted">{communeDescription || t.noDescription}</p>
        </div>
      </div>

      <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h5 class="card-title mb-0">{t.communeDetails}</h5>
              {#if isCurrentUserHead || userPermissions.isAdmin}
                <div class="d-flex gap-1">
                  <button class="btn btn-outline-primary btn-sm" onclick={handleEditClick}>
                    {t.edit}
                  </button>
                  <button class="btn btn-outline-danger btn-sm" onclick={handleDeleteClick}>
                    {t.delete}
                  </button>
                </div>
              {/if}
            </div>
            <hr />
            <div class="d-flex justify-content-between mb-2">
              <span>{t.members}:</span>
              <span class="badge bg-primary">{commune.memberCount}</span>
            </div>
            <div class="d-flex justify-content-between mb-2">
              <span>{t.headMember}:</span>
              <span class="text-muted">
                {getAppropriateLocalization(commune.headMember.name)}
              </span>
            </div>
            <div class="d-flex justify-content-between">
              <span>{t.created}:</span>
              <span class="text-muted">
                {formatDate(new Date(commune.createdAt), t.dateFormatLocale)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Join Request Section (for non-members) -->
    {#if userPermissions.canRequestJoin}
      <div class="alert alert-info d-flex justify-content-between align-items-center mt-4">
        <div>
          <strong>{t.interestedInJoining}</strong>
          <p class="mb-0 small text-muted">{t.joinRequestDescription}</p>
        </div>
        <button class="btn btn-success" onclick={handleJoinRequest} disabled={isSendingJoinRequest}>
          {#if isSendingJoinRequest}
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            {t.sendingJoinRequest}
          {:else}
            {t.requestToJoin}
          {/if}
        </button>
      </div>
    {:else if userPermissions.hasPendingJoinRequest}
      <div class="alert alert-warning mt-4">
        <strong>{t.requestPending}</strong>
        <p class="mb-0 small">{t.joinRequestPendingDescription}</p>
      </div>
    {/if}

    <!-- Members Section -->
    <div class="d-flex justify-content-between align-items-center mt-5 mb-4">
      <h3 class="mb-0">{t.members} ({members.length})</h3>
      {#if userPermissions.canInvite}
        <div class="d-flex gap-2 flex-wrap">
          <a href={toLocaleHref(`/communes/${communeId}/invitations`)} class="btn btn-outline-info">
            {t.manageInvitations}
          </a>
          <a
            href={toLocaleHref(`/communes/${communeId}/join-requests`)}
            class="btn btn-outline-warning"
          >
            {t.manageJoinRequests}
          </a>
          <button class="btn btn-outline-primary" onclick={handleInviteClick}>
            {t.invite}
          </button>
          <!-- {#if isCurrentUserHead}
            <button class="btn btn-primary" onclick={handleAddMemberClick}>
              {t.addMember}
            </button>
          {/if} -->
        </div>
      {/if}
    </div>

    {#if members.length === 0}
      <div class="alert alert-info">{t.noMembers}</div>
    {:else}
      <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
        {#each members as member (member.id)}
          <div class="col">
            <MemberCard
              id={member.id}
              actorType={member.actorType}
              actorId={member.actorId}
              name={member.name ?? []}
              isHead={member.actorType === commune.headMember.actorType &&
                member.actorId === commune.headMember.actorId}
              joinedAt={member.joinedAt}
              {communeId}
              {isCurrentUserHead}
              isCurrentUserAdmin={userPermissions.isAdmin}
              {locale}
              onMemberRemoved={handleMemberRemoved}
              onHeadTransferred={handleHeadTransferred}
              images={member.images || []}
              {toLocaleHref}
              {getAppropriateLocalization}
            />
          </div>
        {/each}
      </div>
    {/if}

    <!-- Add Member Modal -->
    <AddMemberModal
      show={showAddMemberModal}
      onHide={handleAddMemberModalClose}
      {communeId}
      {locale}
      onMemberAdded={handleMemberAdded}
    />

    <!-- Edit Commune Modal -->
    <EditCommuneModal
      show={showEditModal}
      onHide={handleEditModalClose}
      communeData={commune}
      {locale}
      onCommuneUpdated={handleCommuneUpdated}
    />

    <!-- Invite User Modal -->
    <InviteUserModal
      show={showInviteModal}
      onHide={handleInviteModalClose}
      {communeId}
      {locale}
      onInviteSent={handleInviteSent}
    />
  {/if}
</div>

<!-- Delete Confirmation Modal -->
{#if showDeleteModal}
  <!-- svelte-ignore a11y_click_events_have_key_events -->
  <!-- svelte-ignore a11y_no_static_element_interactions -->
  <div
    class="modal fade show"
    style="display: block; background-color: rgba(0,0,0,0.5);"
    tabindex="-1"
    aria-modal="true"
    role="dialog"
    onclick={closeDeleteModal}
    onkeydown={(e) => e.key === "Escape" && closeDeleteModal()}
  >
    <!-- svelte-ignore a11y_click_events_have_key_events -->
    <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
    <div
      class="modal-dialog modal-dialog-centered"
      role="document"
      onclick={(e) => e.stopPropagation()}
    >
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{t.confirmDelete}</h5>
          <button type="button" class="btn-close" aria-label="Close" onclick={closeDeleteModal}
          ></button>
        </div>
        <div class="modal-body">
          {#if error}
            <div class="alert alert-danger" role="alert">
              {error}
            </div>
          {/if}
          <p>
            {t.confirmDeleteMessage}
          </p>
          <div class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {t.deleteWarning}
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" onclick={closeDeleteModal}>
            {t.cancel}
          </button>
          <button
            type="button"
            class="btn btn-danger"
            onclick={handleConfirmDelete}
            disabled={isDeleting}
          >
            {#if isDeleting}
              <span class="spinner-border spinner-border-sm me-2" role="status"></span>
              {t.deleting}
            {:else}
              {t.confirmDelete}
            {/if}
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}
