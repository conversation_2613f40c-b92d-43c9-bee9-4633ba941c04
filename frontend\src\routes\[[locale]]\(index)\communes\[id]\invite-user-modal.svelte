<script lang="ts">
  import { fetchWithAuth } from "$lib";

  interface Props {
    show: boolean;
    communeId: string;
    locale: "en" | "ru";
    onHide: () => void;
    onInviteSent: () => void;
  }

  const { show, communeId, locale, onHide, onInviteSent }: Props = $props();

  const i18n = {
    en: {
      title: "Invite User to Commune",
      userId: "User ID",
      userIdPlaceholder: "Enter user ID to invite",
      invite: "Send Invitation",
      cancel: "Cancel",
      inviting: "Sending invitation...",
      success: "Invitation sent successfully!",
      errorInviting: "Failed to send invitation",
      userIdRequired: "User ID is required",
      invalidUserId: "Please enter a valid user ID",
    },
    ru: {
      title: "Пригласить пользователя в коммуну",
      userId: "ID пользователя",
      userIdPlaceholder: "Введите ID пользователя для приглашения",
      invite: "Отправить приглашение",
      cancel: "Отмена",
      inviting: "Отправляем приглашение...",
      success: "Приглашение отправлено успешно!",
      errorInviting: "Не удалось отправить приглашение",
      userIdRequired: "ID пользователя обязателен",
      invalidUserId: "Пожалуйста, введите корректный ID пользователя",
    },
  };

  const t = $derived(i18n[locale]);

  let userId = $state("");
  let isInviting = $state(false);
  let error = $state<string | null>(null);
  let success = $state<string | null>(null);
  let userIdInput = $state<HTMLInputElement>();

  // Reset form when modal is shown/hidden
  $effect(() => {
    if (show) {
      userId = "";
      error = null;
      success = null;
      isInviting = false;
      // Focus the input after a short delay to ensure modal is rendered
      setTimeout(() => {
        userIdInput?.focus();
      }, 100);
    }
  });

  async function handleInvite() {
    if (!userId.trim()) {
      error = t.userIdRequired;
      return;
    }

    // Basic validation for user ID format (assuming it's a string ID)
    if (userId.trim().length < 3) {
      error = t.invalidUserId;
      return;
    }

    isInviting = true;
    error = null;
    success = null;

    try {
      const response = await fetchWithAuth("/api/commune/invitation", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          communeId,
          userId: userId.trim(),
        }),
      });

      if (!response.ok) {
        let errorMessage = `${t.errorInviting}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          } else if (typeof errorData === "string") {
            errorMessage = errorData;
          }
        } catch {
          // If JSON parsing fails, try text
          try {
            const errorText = await response.text();
            if (errorText) {
              errorMessage = errorText;
            }
          } catch {
            // Use default error message
          }
        }
        throw new Error(errorMessage);
      }

      success = t.success;
      onInviteSent();

      // Close modal after a short delay to show success message
      setTimeout(() => {
        onHide();
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorInviting;
      console.error("Error sending invitation:", err);
    } finally {
      isInviting = false;
    }
  }

  function handleCancel() {
    onHide();
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === "Enter" && !isInviting && userId.trim()) {
      event.preventDefault();
      handleInvite();
    } else if (event.key === "Escape") {
      event.preventDefault();
      handleCancel();
    }
  }
</script>

{#if show}
  <!-- Modal backdrop -->
  <div class="modal-backdrop fade show"></div>

  <!-- Modal -->
  <div class="modal fade show d-block" tabindex="-1" role="dialog" onkeydown={handleKeydown}>
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{t.title}</h5>
          <button
            type="button"
            class="btn-close"
            aria-label="Close"
            onclick={handleCancel}
            disabled={isInviting}
          ></button>
        </div>

        <div class="modal-body">
          {#if success}
            <div class="alert alert-success" role="alert">
              <i class="bi bi-check-circle me-2"></i>
              {success}
            </div>
          {:else}
            <form
              onsubmit={(e) => {
                e.preventDefault();
                handleInvite();
              }}
            >
              <div class="mb-3">
                <label for="userId" class="form-label">{t.userId}</label>
                <input
                  bind:this={userIdInput}
                  type="text"
                  class="form-control"
                  id="userId"
                  bind:value={userId}
                  placeholder={t.userIdPlaceholder}
                  disabled={isInviting}
                  onkeydown={handleKeydown}
                  required
                />
              </div>

              {#if error}
                <div class="alert alert-danger" role="alert">
                  <i class="bi bi-exclamation-triangle me-2"></i>
                  {error}
                </div>
              {/if}
            </form>
          {/if}
        </div>

        {#if !success}
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              onclick={handleCancel}
              disabled={isInviting}
            >
              {t.cancel}
            </button>
            <button
              type="button"
              class="btn btn-primary"
              onclick={handleInvite}
              disabled={isInviting || !userId.trim()}
            >
              {#if isInviting}
                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                {t.inviting}
              {:else}
                {t.invite}
              {/if}
            </button>
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}

<style>
  .modal.show {
    background-color: rgba(0, 0, 0, 0.5);
  }

  .modal-backdrop.show {
    opacity: 0.5;
  }
</style>
