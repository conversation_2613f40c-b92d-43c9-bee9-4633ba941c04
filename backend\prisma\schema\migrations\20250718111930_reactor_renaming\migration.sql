/*
  Warnings:

  - You are about to drop the column `group_id` on the `reactor_posts` table. All the data in the column will be lost.
  - You are about to drop the `_post_description` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_post_images` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_post_tags` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_post_title` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_reactor_group_description` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_reactor_group_name` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `posts` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `reactor_groups` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "_post_description" DROP CONSTRAINT "_post_description_A_fkey";

-- DropForeignKey
ALTER TABLE "_post_description" DROP CONSTRAINT "_post_description_B_fkey";

-- DropForeignKey
ALTER TABLE "_post_images" DROP CONSTRAINT "_post_images_A_fkey";

-- DropForeignKey
ALTER TABLE "_post_images" DROP CONSTRAINT "_post_images_B_fkey";

-- DropForeignKey
ALTER TABLE "_post_tags" DROP CONSTRAINT "_post_tags_A_fkey";

-- DropForeignKey
ALTER TABLE "_post_tags" DROP CONSTRAINT "_post_tags_B_fkey";

-- DropForeignKey
ALTER TABLE "_post_title" DROP CONSTRAINT "_post_title_A_fkey";

-- DropForeignKey
ALTER TABLE "_post_title" DROP CONSTRAINT "_post_title_B_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_group_description" DROP CONSTRAINT "_reactor_group_description_A_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_group_description" DROP CONSTRAINT "_reactor_group_description_B_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_group_name" DROP CONSTRAINT "_reactor_group_name_A_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_group_name" DROP CONSTRAINT "_reactor_group_name_B_fkey";

-- DropForeignKey
ALTER TABLE "reactor_groups" DROP CONSTRAINT "reactor_groups_head_user_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_groups" DROP CONSTRAINT "reactor_groups_hub_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_groups" DROP CONSTRAINT "reactor_groups_image_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_posts" DROP CONSTRAINT "reactor_posts_group_id_fkey";

-- DropIndex
DROP INDEX "reactor_posts_author_id_hub_id_group_id_idx";

-- AlterTable
ALTER TABLE "reactor_posts" DROP COLUMN "group_id",
ADD COLUMN     "community_id" TEXT;

-- DropTable
DROP TABLE "_post_description";

-- DropTable
DROP TABLE "_post_images";

-- DropTable
DROP TABLE "_post_tags";

-- DropTable
DROP TABLE "_post_title";

-- DropTable
DROP TABLE "_reactor_group_description";

-- DropTable
DROP TABLE "_reactor_group_name";

-- DropTable
DROP TABLE "posts";

-- DropTable
DROP TABLE "reactor_groups";

-- DropEnum
DROP TYPE "post_status";

-- CreateTable
CREATE TABLE "reactor_communities" (
    "id" TEXT NOT NULL,
    "hub_id" TEXT,
    "head_user_id" TEXT NOT NULL,
    "image_id" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "reactor_communities_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_reactor_community_name" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_community_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_community_description" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_community_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "reactor_communities_hub_id_idx" ON "reactor_communities"("hub_id");

-- CreateIndex
CREATE INDEX "_reactor_community_name_B_index" ON "_reactor_community_name"("B");

-- CreateIndex
CREATE INDEX "_reactor_community_description_B_index" ON "_reactor_community_description"("B");

-- CreateIndex
CREATE INDEX "reactor_posts_author_id_hub_id_community_id_idx" ON "reactor_posts"("author_id", "hub_id", "community_id");

-- AddForeignKey
ALTER TABLE "reactor_posts" ADD CONSTRAINT "reactor_posts_community_id_fkey" FOREIGN KEY ("community_id") REFERENCES "reactor_communities"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_communities" ADD CONSTRAINT "reactor_communities_hub_id_fkey" FOREIGN KEY ("hub_id") REFERENCES "reactor_hubs"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_communities" ADD CONSTRAINT "reactor_communities_head_user_id_fkey" FOREIGN KEY ("head_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_communities" ADD CONSTRAINT "reactor_communities_image_id_fkey" FOREIGN KEY ("image_id") REFERENCES "images"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_community_name" ADD CONSTRAINT "_reactor_community_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_community_name" ADD CONSTRAINT "_reactor_community_name_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_communities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_community_description" ADD CONSTRAINT "_reactor_community_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_community_description" ADD CONSTRAINT "_reactor_community_description_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_communities"("id") ON DELETE CASCADE ON UPDATE CASCADE;
