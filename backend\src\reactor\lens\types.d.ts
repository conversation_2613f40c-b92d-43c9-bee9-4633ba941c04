type PlainValue = {
    type: "plain";
    value: string;
};

type ArrayValue = {
    type: "array";
    value: string[];
};

type Value = PlainValue | ArrayValue;

type Comparable =
    | "hub"
    | "community"
    | "author"
    | "rating"
    | "usefulness"
    | "tag"
    | "difficulty"
    | "title"
    | "body"
    | "duration"
    | "age";

type And = {
    type: "and";
    left: Clause;
    right: Clause;
};

type Or = {
    type: "or";
    left: Clause;
    right: Clause;
};

type Eq = {
    type: "eq";
    left: Comparable;
    right: Value;
};

type Neq = {
    type: "neq";
    left: Comparable;
    right: Value;
};

type Like = {
    type: "like";
    left: Comparable;
    right: Value;
};

type Gt = {
    type: "gt";
    left: Comparable;
    right: Value;
};

type Gte = {
    type: "gte";
    left: Comparable;
    right: Value;
};

type Lt = {
    type: "lt";
    left: Comparable;
    right: Value;
};

type Lte = {
    type: "lte";
    left: Comparable;
    right: Value;
};

type Clause = And | Or | Eq | Neq | Like | Gt | Gte | Lt | Lte;

export {};
