import type { Infer } from "./types";

import { z } from "zod";
import { id, ImagesSchema, JsonStringToObject, LocalizationsSchema } from "./common";

export type CommuneMemberType = Infer<typeof CommuneMemberTypeSchema>;
export const CommuneMemberTypeSchema = z.enum(["user"]);

export const communeMemberActorType = CommuneMemberTypeSchema;
export const communeMemberName = LocalizationsSchema;

export type CommuneMember = Infer<typeof CommuneMemberSchema>;
export const CommuneMemberSchema = z.object({
    id,

    actorType: communeMemberActorType,
    actorId: id,

    name: communeMemberName,

    images: ImagesSchema,

    joinedAt: z.date(),
    leftAt: z.date().nullable(),
});

export type CommuneMembers = Infer<typeof CommuneMembersSchema>;
export const CommuneMembersSchema = z.array(CommuneMemberSchema);

export const communeName = LocalizationsSchema;
export const communeDescription = LocalizationsSchema;

export type Commune = Infer<typeof CommuneSchema>;
export const CommuneSchema = z.object({
    id,

    name: LocalizationsSchema,
    description: LocalizationsSchema,

    headMember: z.object({
        actorType: communeMemberActorType,
        actorId: id,

        name: communeMemberName,
    }),

    memberCount: z.number().int().positive(),

    images: ImagesSchema,

    createdAt: z.date(),
    updatedAt: z.date(),
});

export type Communes = Infer<typeof CommunesSchema>;
export const CommunesSchema = z.array(CommuneSchema);

export type CreateCommuneRequest = Infer<typeof CreateCommuneRequestSchema>;
export const CreateCommuneRequestSchema = JsonStringToObject({
    headUserId: id.optional(),

    name: communeName,
    description: communeDescription,

    // Images are handled separately via file upload
});

export type UpdateCommuneRequest = Infer<typeof UpdateCommuneRequestSchema>;
export const UpdateCommuneRequestSchema = z.object({
    name: communeName,
    description: communeDescription,
});

export type CreateCommuneMemberRequest = Infer<typeof CreateCommuneMemberRequestSchema>;
export const CreateCommuneMemberRequestSchema = z.object({
    userId: id,
});

export type CommuneInvitationStatus = Infer<typeof CommuneInvitationStatusSchema>;
export const CommuneInvitationStatusSchema = z.enum(["pending", "accepted", "rejected", "expired"]);

export type CommuneInvitation = Infer<typeof CommuneInvitationSchema>;
export const CommuneInvitationSchema = z.object({
    id,

    communeId: id,
    userId: id,

    status: CommuneInvitationStatusSchema,

    createdAt: z.date(),
    updatedAt: z.date(),
});

export type CommuneInvitations = Infer<typeof CommuneInvitationsSchema>;
export const CommuneInvitationsSchema = z.array(CommuneInvitationSchema);

export type CreateCommuneInvitationRequest = Infer<typeof CreateCommuneInvitationRequestSchema>;
export const CreateCommuneInvitationRequestSchema = z.object({
    communeId: id,
    userId: id,
});

export type CommuneJoinRequestStatus = Infer<typeof CommuneJoinRequestStatusSchema>;
export const CommuneJoinRequestStatusSchema = z.enum(["pending", "accepted", "rejected"]);

export type CommuneJoinRequest = Infer<typeof CommuneJoinRequestSchema>;
export const CommuneJoinRequestSchema = z.object({
    id,

    communeId: id,
    userId: id,

    status: CommuneJoinRequestStatusSchema,

    createdAt: z.date(),
    updatedAt: z.date(),
});

export type CommuneJoinRequests = Infer<typeof CommuneJoinRequestsSchema>;
export const CommuneJoinRequestsSchema = z.array(CommuneJoinRequestSchema);

export type CreateCommuneJoinRequestRequest = Infer<typeof CreateCommuneJoinRequestRequestSchema>;
export const CreateCommuneJoinRequestRequestSchema = z.object({
    communeId: id,
    userId: id,
});

export type TransferHeadStatusRequest = Infer<typeof TransferHeadStatusRequestSchema>;
export const TransferHeadStatusRequestSchema = z.object({
    newHeadUserId: id,
});
