{"version": 3, "file": "email-otp.service.js", "sourceRoot": "", "sources": ["../../src/email/email-otp.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmE;AACnE,2CAA+C;AAC/C,6BAAwB;AACxB,6CAA6C;AAE7C,6DAA0D;AAmBnD,IAAM,eAAe,GAArB,MAAM,eAAe;IACxB,YACqB,aAA4B,EAC5B,MAAqB;QADrB,kBAAa,GAAb,aAAa,CAAe;QAC5B,WAAM,GAAN,MAAM,CAAe;IACvC,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,gBAAmC;QAC5C,MAAM,cAAc,GAAG,OAAC,CAAC,MAAM;aAC1B,MAAM,EAAE;aACR,GAAG,EAAE;aACL,QAAQ,EAAE;aACV,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC;QAE7D,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACzC,IAAI,EAAE;gBACF,KAAK,EAAE,gBAAgB,CAAC,KAAK;gBAC7B,GAAG,EAAE,gBAAgB,CAAC,GAAG;gBACzB,SAAS,EAAE,gBAAgB,CAAC,SAAS;gBACrC,SAAS,EAAE,gBAAgB,CAAC,SAAS;gBACrC,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC;aACnD;SACJ,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC;IACf,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,eAAiC;QACzC,MAAM,KAAK,GAAG;YACV,KAAK,EAAE,eAAe,CAAC,KAAK;YAC5B,GAAG,EAAE,eAAe,CAAC,GAAG;YACxB,SAAS,EAAE;gBACP,EAAE,EAAE,IAAI,IAAI,EAAE;aACjB;YACD,SAAS,EAAE,IAAI;SACiB,CAAC;QAIrC,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YAC5C,KAAK,EAAE,KAAK;SACf,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,MAAM,IAAI,8BAAqB,CAAC,GAAG,IAAA,iBAAQ,EAAC,aAAa,CAAC,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,gBAAmC;QAChD,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC7B,KAAK,EAAE;gBACH,EAAE,EAAE,gBAAgB,CAAC,EAAE;aAC1B;YACD,IAAI,EAAE;gBACF,SAAS,EAAE,IAAI,IAAI,EAAE;aACxB;SACJ,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,gBAAmC;QAC5C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC7B,KAAK,EAAE;gBACH,EAAE,EAAE,gBAAgB,CAAC,EAAE;aAC1B;SACJ,CAAC,CAAC;IACP,CAAC;CACJ,CAAA;AAnEY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAG2B,sBAAa;QACpB,8BAAa;GAHjC,eAAe,CAmE3B"}