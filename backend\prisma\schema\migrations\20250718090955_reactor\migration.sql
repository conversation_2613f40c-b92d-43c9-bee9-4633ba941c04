/*
  Warnings:

*/
-- DropForeignKey
ALTER TABLE "reactor_groups" DROP CONSTRAINT "reactor_groups_hub_id_fkey";

-- DropIndex
DROP INDEX "reactor_posts_author_id_idx";

-- DropIndex
DROP INDEX "reactor_posts_group_id_idx";

-- DropIndex
DROP INDEX "reactor_posts_hub_id_idx";

-- DropIndex
DROP INDEX "reactor_ratings_entity_type_entity_id_idx";

-- DropIndex
DROP INDEX "reactor_ratings_user_id_idx";

-- DropIndex
DROP INDEX "reactor_usefulnesses_entity_type_entity_id_idx";

-- DropIndex
DROP INDEX "reactor_usefulnesses_user_id_idx";

-- AlterTable
ALTER TABLE "reactor_groups" ADD COLUMN     "image_id" TEXT,
ALTER COLUMN "hub_id" DROP NOT NULL;

-- AlterTable
ALTER TABLE "reactor_hubs" ADD COLUMN     "image_id" TEXT;

-- CreateTable
CREATE TABLE "_tag_name" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_tag_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_tag_name_B_index" ON "_tag_name"("B");

-- CreateIndex
CREATE INDEX "localizations_value_idx" ON "localizations"("value");

-- CreateIndex
CREATE INDEX "reactor_posts_author_id_hub_id_group_id_idx" ON "reactor_posts"("author_id", "hub_id", "group_id");

-- CreateIndex
CREATE INDEX "reactor_ratings_user_id_entity_type_entity_id_idx" ON "reactor_ratings"("user_id", "entity_type", "entity_id");

-- CreateIndex
CREATE INDEX "reactor_usefulnesses_user_id_entity_type_entity_id_idx" ON "reactor_usefulnesses"("user_id", "entity_type", "entity_id");

-- AddForeignKey
ALTER TABLE "reactor_hubs" ADD CONSTRAINT "reactor_hubs_image_id_fkey" FOREIGN KEY ("image_id") REFERENCES "images"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_groups" ADD CONSTRAINT "reactor_groups_hub_id_fkey" FOREIGN KEY ("hub_id") REFERENCES "reactor_hubs"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_groups" ADD CONSTRAINT "reactor_groups_image_id_fkey" FOREIGN KEY ("image_id") REFERENCES "images"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_tag_name" ADD CONSTRAINT "_tag_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_tag_name" ADD CONSTRAINT "_tag_name_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;
