// @ts-nocheck
import type { PageLoad } from "./$types";
import type { Auth, Reactor } from "@commune/api";

import { handleUnauthorized } from "$lib";

const PAGE_SIZE = 20;

async function fetchHubs(
  fetch: typeof globalThis.fetch,
  searchQuery: string,
  url: URL,
): Promise<Reactor.GetHubsOutput> {
  const trimmedSearchQuery = searchQuery.trim();
  const searchBody = trimmedSearchQuery ? { query: trimmedSearchQuery } : {};

  const response = await fetch(
    `/api/reactor/hub?body=${encodeURIComponent(
      JSON.stringify(searchBody),
    )}`,
  );

  handleUnauthorized(response, url);

  if (!response.ok) {
    throw new Error(`Failed to fetch hubs: ${response.statusText}`);
  }

  return response.json();
}

async function fetchCurrentUser(
  fetch: typeof globalThis.fetch,
  url: URL,
): Promise<Auth.GetMeResponse | null> {
  try {
    const response = await fetch("/api/auth/me");

    handleUnauthorized(response, url);

    return response.ok ? await response.json() : null;
  } catch {
    return null;
  }
}

export const load = async ({ fetch, url }: Parameters<PageLoad>[0]) => {
  const searchQuery = decodeURIComponent(url.searchParams.get("search") || "");

  const [user, hubs] = await Promise.all([
    fetchCurrentUser(fetch, url),
    fetchHubs(fetch, searchQuery, url).catch(() => []), // Fallback to empty array on error
  ]);

  return {
    hubs,
    searchQuery,
    isHasMoreHubs: hubs.length === PAGE_SIZE,
    user,
    pageSize: PAGE_SIZE,
  };
};
