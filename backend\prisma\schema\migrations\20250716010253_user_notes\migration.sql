-- CreateTable
CREATE TABLE "user_ratings" (
    "id" TEXT NOT NULL,
    "source_user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "value" SMALLINT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_ratings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_karma_spendable_points" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "points" SMALLINT NOT NULL DEFAULT 0,

    CONSTRAINT "user_karma_spendable_points_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_karma_given_points" (
    "id" TEXT NOT NULL,
    "source_user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "user_karma_given_points_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_feedbacks" (
    "id" TEXT NOT NULL,
    "source_user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "value" SMALLINT NOT NULL,
    "is_anonymous" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "user_feedbacks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_notes" (
    "id" TEXT NOT NULL,
    "source_user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "user_notes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_user_karma_given_point_comment" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_user_karma_given_point_comment_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_user_feedback_text" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_user_feedback_text_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_notes_source_user_id_target_user_id_key" ON "user_notes"("source_user_id", "target_user_id");

-- CreateIndex
CREATE INDEX "_user_karma_given_point_comment_B_index" ON "_user_karma_given_point_comment"("B");

-- CreateIndex
CREATE INDEX "_user_feedback_text_B_index" ON "_user_feedback_text"("B");

-- AddForeignKey
ALTER TABLE "_user_karma_given_point_comment" ADD CONSTRAINT "_user_karma_given_point_comment_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_karma_given_point_comment" ADD CONSTRAINT "_user_karma_given_point_comment_B_fkey" FOREIGN KEY ("B") REFERENCES "user_karma_given_points"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_feedback_text" ADD CONSTRAINT "_user_feedback_text_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_feedback_text" ADD CONSTRAINT "_user_feedback_text_B_fkey" FOREIGN KEY ("B") REFERENCES "user_feedbacks"("id") ON DELETE CASCADE ON UPDATE CASCADE;
