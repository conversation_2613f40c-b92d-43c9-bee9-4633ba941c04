"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TagService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagService = void 0;
const common_1 = require("@nestjs/common");
const utils_1 = require("../utils");
const prisma_service_1 = require("../prisma/prisma.service");
const errors_1 = require("../common/errors");
let TagService = TagService_1 = class TagService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(TagService_1.name);
    }
    async getTags(input, user) {
        const { ids, query } = input;
        const tags = await this.prisma.tag.findMany({
            where: Object.assign({}, ids && { id: { in: ids } }, query && { name: (0, utils_1.toPrismaLocalizationsWhere)(query) }, !user.isAdmin && { deletedAt: null }),
            select: {
                id: true,
                name: {
                    select: {
                        locale: true,
                        value: true,
                    },
                },
                deletedAt: user.isAdmin,
            },
        });
        return tags;
    }
    async createTag(input, user) {
        if (!user.isAdmin) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin"));
        }
        const tag = await this.prisma.tag.create({
            data: {
                name: {
                    create: (0, utils_1.toPrismaLocalizations)(input.name, "name"),
                },
            },
        });
        return tag;
    }
    async updateTag(id, input, user) {
        if (!user.isAdmin) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin"));
        }
        const tag = await this.prisma.tag.update({
            where: { id },
            data: {
                name: {
                    deleteMany: {},
                    create: (0, utils_1.toPrismaLocalizations)(input.name, "name"),
                },
            },
        });
        return tag;
    }
    async deleteTag(id, user) {
        if (!user.isAdmin) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin"));
        }
        const tag = await this.prisma.tag.update({
            where: { id },
            data: {
                deletedAt: new Date(),
            },
        });
        return tag;
    }
};
exports.TagService = TagService;
exports.TagService = TagService = TagService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], TagService);
//# sourceMappingURL=tag.service.js.map