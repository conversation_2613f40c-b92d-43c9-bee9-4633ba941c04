"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const zod_1 = require("zod");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const errors_1 = require("../common/errors");
const email_service_1 = require("../email/email.service");
const user_service_1 = require("../user/user.service");
const email_otp_service_1 = require("../email/email-otp.service");
const coerceBoolean = (data) => zod_1.z.coerce.boolean().parse(data);
const nonEmptyString = (data) => zod_1.z.string().nonempty().parse(data);
let AuthService = class AuthService {
    constructor(configService, emailService, userService, userOtpService) {
        this.configService = configService;
        this.emailService = emailService;
        this.userService = userService;
        this.userOtpService = userOtpService;
        this.disableRegisterOtpCheck = coerceBoolean(this.configService.get("DISABLE_REGISTER_OTP_CHECK"));
        this.disableLoginOtpCheck = coerceBoolean(this.configService.get("DISABLE_LOGIN_OTP_CHECK"));
        this.instanceName = nonEmptyString(this.configService.get("INSTANCE_NAME"));
        this.domain = nonEmptyString(this.configService.get("INSTANCE_EMAIL_DOMAIN"));
        this.otpSender = nonEmptyString(this.configService.get("OTP_EMAIL_SENDER"));
    }
    createSessionUser(user) {
        return {
            id: user.id,
            email: user.email,
            role: user.role,
        };
    }
    generateOtp() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }
    async otp(dto) {
        const otp = this.generateOtp();
        await this.userOtpService.create({
            email: dto.email,
            otp,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });
        console.log({ otp, email: dto.email });
        return await this.emailService.send({
            from: this.emailService.joinAddress(this.otpSender, this.domain),
            to: [dto.email],
            subject: `${this.instanceName} - OTP`,
            text: `Your OTP is ${otp}.`,
        });
    }
    async login(dto) {
        const user = await this.userService.getByEmail(dto.email);
        if (!user) {
            throw new common_1.UnauthorizedException(...(0, errors_1.getError)("user_not_found"));
        }
        if (!this.disableLoginOtpCheck) {
            const userOtp = await this.userOtpService.check({
                email: dto.email,
                otp: dto.otp,
            });
            await this.userOtpService.softDelete({
                id: userOtp.id,
            });
        }
        const sessionUser = this.createSessionUser(user);
        return {
            user: sessionUser,
        };
    }
    async register(dto) {
        {
            const user = await this.userService.getByEmail(dto.email);
            if (user) {
                throw new common_1.UnauthorizedException(...(0, errors_1.getError)("user_already_exists"));
            }
        }
        if (!this.disableRegisterOtpCheck) {
            const otp = await this.userOtpService.check({
                email: dto.email,
                otp: dto.otp,
            });
            await this.userOtpService.softDelete({
                id: otp.id,
            });
        }
        const user = await this.userService.create({
            referrerId: dto.referrerId,
            email: dto.email,
        });
        const sessionUser = this.createSessionUser(user);
        return {
            user: sessionUser,
        };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        email_service_1.EmailService,
        user_service_1.UserService,
        email_otp_service_1.EmailOtpService])
], AuthService);
//# sourceMappingURL=auth.service.js.map