import { CurrentUser } from "src/auth/types";
import { ReactorService } from "../reactor.service";
import { Common, Reactor } from "@commune/api";
export declare class ReactorController {
    private readonly reactorService;
    constructor(reactorService: ReactorService);
    createMockPost(): Promise<{
        id: string;
        authorId: string;
        hubId: string | null;
        communityId: string | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        deleteReason: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    getPosts2(): Promise<import("../reactor.service").PostsResponse>;
    getPosts(pagination: Common.Pagination, user: CurrentUser): Promise<{
        items: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            author: {
                id: string;
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                avatar: string | null;
            };
            rating: {
                status: "like" | "dislike" | null;
                likes: number;
                dislikes: number;
            };
            usefulness: {
                value: number | null;
                count: number;
                totalValue: number | null;
            };
            title: {
                value: string;
                locale: "en" | "ru";
            }[];
            body: {
                value: string;
                locale: "en" | "ru";
            }[];
            tags: string[];
            deletedAt?: Date | null | undefined;
        }[];
        total: number;
    }>;
    getPost(id: string, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        };
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
        body: {
            value: string;
            locale: "en" | "ru";
        }[];
        tags: string[];
        deletedAt?: Date | null | undefined;
    }>;
    createPost(body: Reactor.CreatePostRequest, user: CurrentUser): Promise<{
        id: string;
    }>;
    updatePost(id: string, body: Reactor.UpdatePostRequest, user: CurrentUser): Promise<boolean>;
    updatePostRating(id: string, body: Reactor.UpdatePostRatingRequest, user: CurrentUser): Promise<{
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }>;
    updatePostUsefulness(id: string, body: Reactor.UpdatePostUsefulnessRequest, user: CurrentUser): Promise<{
        value: number | null;
        count: number;
        totalValue: number | null;
    }>;
    deletePost(id: string, body: Reactor.DeletePostRequest, user: CurrentUser): Promise<boolean>;
    getComments(body: Reactor.GetCommentsRequest, user: CurrentUser): Promise<{
        items: {
            path: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            author: {
                id: string;
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                avatar: string | null;
            } | null;
            rating: {
                status: "like" | "dislike" | null;
                likes: number;
                dislikes: number;
            };
            body: {
                value: string;
                locale: "en" | "ru";
            }[] | null;
            isAnonymous: boolean;
            anonimityReason: string | null;
            childrenCount: number;
            deleteReason: string | null;
            deletedAt?: Date | null | undefined;
        }[];
        total: number;
    }>;
    getComment(id: string, user: CurrentUser): Promise<{
        path: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        } | null;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        body: {
            value: string;
            locale: "en" | "ru";
        }[] | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        childrenCount: number;
        deleteReason: string | null;
        deletedAt?: Date | null | undefined;
    }>;
    createComment(body: Reactor.CreateCommentRequest, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateComment(id: string, body: Reactor.UpdateCommentRequest, user: CurrentUser): Promise<boolean>;
    updateCommentRating(id: string, body: Reactor.UpdateCommentRatingRequest, user: CurrentUser): Promise<{
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }>;
    anonimifyComment(id: string, body: Reactor.AnonimifyCommentRequest, user: CurrentUser): Promise<boolean>;
    deleteComment(id: string, body: Reactor.DeleteCommentRequest, user: CurrentUser): Promise<boolean>;
    getLenses(user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: string;
        userId: string;
        code: string;
        sql: string;
    }[]>;
    createLens(body: Reactor.CreateLensRequest, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateLens(id: string, body: Reactor.UpdateLensRequest, user: CurrentUser): Promise<boolean>;
    deleteLens(id: string, user: CurrentUser): Promise<void>;
    getHubs(pagination: Common.Pagination, body: Reactor.GetHubsInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
        headUser: {
            id: string;
            email: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            images: {
                id: string;
                url: string;
                createdAt: Date;
                updatedAt: Date;
            }[];
        };
        image: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        } | null;
        deletedAt?: Date | null | undefined;
    }[]>;
    createHub(body: Reactor.CreateHubInput, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateHub(id: string, body: Reactor.UpdateHubInput, user: CurrentUser): Promise<void>;
    updateHubImage(id: string, user: CurrentUser, file: Express.Multer.File): Promise<void>;
    deleteHubImage(id: string, user: CurrentUser): Promise<void>;
    getCommunities(pagination: Common.Pagination, body: Reactor.GetCommunitiesInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
        headUser: {
            id: string;
            email: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            images: {
                id: string;
                url: string;
                createdAt: Date;
                updatedAt: Date;
            }[];
        };
        image: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        } | null;
        hub: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            image: {
                id: string;
                url: string;
                createdAt: Date;
                updatedAt: Date;
            } | null;
        } | null;
        deletedAt?: Date | null | undefined;
    }[]>;
    createCommunity(body: Reactor.CreateCommunityInput, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateCommunity(id: string, body: Reactor.UpdateCommunityInput, user: CurrentUser): Promise<void>;
    updateCommunityImage(id: string, user: CurrentUser, file: Express.Multer.File): Promise<void>;
    deleteCommunityImage(id: string, user: CurrentUser): Promise<void>;
}
