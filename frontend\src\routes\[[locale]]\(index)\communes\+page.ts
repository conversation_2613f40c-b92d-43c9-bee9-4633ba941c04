import type { Commune } from "@commune/api";
import type { PageLoad } from "./$types";

import { fixResponseJsonDatesForArray, handleUnauthorized } from "$lib";

export const load: PageLoad = async ({ fetch, url, parent }) => {
  const { user } = await parent();

  const response = await fetch("/api/commune");

  handleUnauthorized(response, url);

  const data: Commune.Communes = response.ok
    ? await response.json().then(fixResponseJsonDatesForArray)
    : [];

  // Fetch pending invitations count if user is logged in
  let pendingInvitationsCount = 0;
  if (user) {
    try {
      // Fetch a reasonable number of invitations to count pending ones
      // Most users won't have more than 50 invitations
      const invitationsResponse = await fetch("/api/commune/invitation?page=1&size=50");
      if (invitationsResponse.ok) {
        const invitations = await invitationsResponse.json();
        // Count pending invitations
        pendingInvitationsCount = invitations.filter((inv: any) => inv.status === "pending").length;
      }
    } catch (error) {
      console.error("Failed to fetch invitations count:", error);
    }
  }

  return {
    communes: data,
    isHasMoreCommunes: data.length === 20, // If we got a full page, there might be more
    pendingInvitationsCount,
    isLoggedIn: !!user,
  };
};
