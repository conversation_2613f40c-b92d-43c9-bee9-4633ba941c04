<script lang="ts">
  import type { Rating, Common } from "@commune/api";
  import { fetchWithAuth } from "$lib";
  import LocalizedTextarea from "../../../components/localized-textarea.svelte";

  const i18n = {
    en: {
      _page: {
        title: "Karma — Commune",
      },
      userNotFound: "User not found",
      karmaHistory: "Karma History",
      changeKarma: "Change Karma",
      karmaModalTitle: "Change Karma",
      giveKarma: "Give Karma (+1)",
      takeKarma: "Take Karma (-1)",
      comment: "Comment",
      commentPlaceholder: "Enter your comment...",
      cancel: "Cancel",
      submit: "Submit",
      submitting: "Submitting...",
      success: "Karma changed successfully",
      errorSubmitting: "Error submitting karma change",
      noKarmaChanges: "No karma changes found",
      loadingMore: "Loading more...",
      errorOccurred: "An error occurred",
      errorFetchingKarma: "Error fetching karma changes",
    },
    ru: {
      _page: {
        title: "Карма — Коммуна",
      },
      userNotFound: "Пользователь не найден",
      karmaHistory: "История кармы",
      changeKarma: "Изменить карму",
      karmaModalTitle: "Изменить карму",
      giveKarma: "Дать карму (+1)",
      takeKarma: "Забрать карму (-1)",
      comment: "Комментарий",
      commentPlaceholder: "Введите ваш комментарий...",
      cancel: "Отмена",
      submit: "Отправить",
      submitting: "Отправка...",
      success: "Карма успешно изменена",
      errorSubmitting: "Ошибка при изменении кармы",
      noKarmaChanges: "Изменения кармы не найдены",
      loadingMore: "Загрузка...",
      errorOccurred: "Произошла ошибка",
      errorFetchingKarma: "Ошибка загрузки изменений кармы",
    },
  };

  const { data } = $props();
  const { user, locale, getAppropriateLocalization, currentUser } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes
  let karmaPoints = $state(data.karmaPoints);
  let error = $state<string | null>(null);

  const PAGE_SIZE = 20;

  let isLoadingMore = $state(false);
  let currentPage = $state(1);
  let isHasMoreKarma = $state(data.isHasMoreKarma);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  // Modal state
  let showModal = $state(false);
  let selectedKarmaType = $state<1 | -1>(1); // 1 for give, -1 for take
  let comment = $state<Common.Localizations>([]);
  let isSubmitting = $state(false);
  let submitError = $state<string | null>(null);
  let submitSuccess = $state(false);

  // Function to load more karma points
  async function loadMoreKarmaPoints() {
    if (isLoadingMore || !isHasMoreKarma) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;
      const response = await fetchWithAuth(
        `/api/rating/${user.id}/karma?page=${nextPage}&size=${PAGE_SIZE}`,
      );

      if (!response.ok) {
        throw new Error(`${t.errorFetchingKarma}: ${response.statusText}`);
      }

      const newKarmaPoints: Rating.GetKarmaPointsResponse = await response.json();

      // Append new karma points to existing list
      karmaPoints = [...karmaPoints, ...newKarmaPoints];
      currentPage = nextPage;

      // Check if there are more karma points to load
      isHasMoreKarma = newKarmaPoints.length === PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  // Set up intersection observer for infinite scroll
  $effect(() => {
    if (!sentinelElement) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && isHasMoreKarma && !isLoadingMore) {
          loadMoreKarmaPoints();
        }
      },
      { threshold: 0.1 },
    );

    observer.observe(sentinelElement);

    return () => {
      observer.disconnect();
    };
  });

  // Handle keyboard events for modal
  $effect(() => {
    if (!showModal) return;

    const handleKeydown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        closeModal();
      }
    };

    document.addEventListener("keydown", handleKeydown);

    return () => {
      document.removeEventListener("keydown", handleKeydown);
    };
  });

  // Derived values
  const userName = $derived(getAppropriateLocalization(user.name));

  // Modal functions
  const openModal = () => {
    showModal = true;
    selectedKarmaType = 1;
    comment = [];
    submitError = null;
    submitSuccess = false;
  };

  const closeModal = () => {
    showModal = false;
    comment = [];
    submitError = null;
    submitSuccess = false;
  };

  const handleSubmitKarma = async () => {
    if (comment.length === 0 || !comment.some((c) => c.value.trim())) {
      submitError = "Comment is required";
      return;
    }

    isSubmitting = true;
    submitError = null;

    try {
      const response = await fetchWithAuth("/api/rating/karma", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          sourceUserId: currentUser.id,
          targetUserId: user.id,
          quantity: selectedKarmaType,
          comment: comment,
        }),
      });

      if (!response.ok) {
        throw new Error(`${t.errorSubmitting}: ${response.statusText}`);
      }

      // Success - refresh the karma points list
      submitSuccess = true;

      // Refresh the first page of karma points
      const refreshResponse = await fetchWithAuth(
        `/api/rating/${user.id}/karma?page=1&size=${PAGE_SIZE}`,
      );
      if (refreshResponse.ok) {
        const refreshedKarmaPoints: Rating.GetKarmaPointsResponse = await refreshResponse.json();
        karmaPoints = refreshedKarmaPoints;
        currentPage = 1;
        isHasMoreKarma = refreshedKarmaPoints.length === PAGE_SIZE;
      }

      // Close modal after a short delay to show success message
      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (err) {
      submitError = err instanceof Error ? err.message : t.errorSubmitting;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  };

  // Get author display name
  const getAuthorDisplayName = (author: Rating.GetKarmaPointsResponse[0]["author"]) => {
    const name = getAppropriateLocalization(author.name);
    return name || author.email;
  };

  // Get author avatar URL
  const getAuthorAvatar = (author: Rating.GetKarmaPointsResponse[0]["author"]) => {
    return author.images && author.images.length > 0 ? author.images[0].url : null;
  };

  // Format quantity with sign
  const formatQuantity = (quantity: number) => {
    return quantity > 0 ? `+${quantity}` : `${quantity}`;
  };

  // Get quantity color class
  const getQuantityColorClass = (quantity: number) => {
    return quantity > 0 ? "text-success" : "text-danger";
  };
</script>

<svelte:head>
  <title>{userName} {t._page.title}</title>
</svelte:head>

<div class="container py-4">
  <div class="responsive-container">
    {#if !user}
      <div class="alert alert-danger" role="alert">
        {t.userNotFound}
      </div>
    {:else}
      <!-- Header with user name and action buttons -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 class="mb-1">{userName}</h2>
          <p class="text-muted mb-0">{t.karmaHistory}</p>
        </div>

        <!-- Action button in top right corner -->
        <div>
          <button class="btn btn-primary btn-sm" onclick={openModal} aria-label={t.changeKarma}>
            <i class="bi bi-arrow-up-down me-1"></i>
            {t.changeKarma}
          </button>
        </div>
      </div>

      <!-- Karma changes list -->
      {#if karmaPoints.length === 0}
        <div class="text-center py-5">
          <p class="text-muted">{t.noKarmaChanges}</p>
        </div>
      {:else}
        {#each karmaPoints as karmaPoint (karmaPoint.id)}
          <div class="card mb-3 shadow-sm">
            <div class="card-body">
              <div class="d-flex align-items-start">
                <!-- Author avatar -->
                <div class="me-3">
                  {#if getAuthorAvatar(karmaPoint.author)}
                    <img
                      src={getAuthorAvatar(karmaPoint.author)}
                      alt={getAuthorDisplayName(karmaPoint.author)}
                      class="rounded-circle"
                      style="width: 48px; height: 48px; object-fit: cover;"
                    />
                  {:else}
                    <div
                      class="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white"
                      style="width: 48px; height: 48px;"
                    >
                      <i class="bi bi-person-fill"></i>
                    </div>
                  {/if}
                </div>

                <!-- Content -->
                <div class="flex-grow-1">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                      <h6 class="mb-1">{getAuthorDisplayName(karmaPoint.author)}</h6>
                    </div>
                    <span class="badge {getQuantityColorClass(karmaPoint.quantity)} fs-6">
                      {formatQuantity(karmaPoint.quantity)}
                    </span>
                  </div>

                  <!-- Comment -->
                  <p class="mb-0 text-muted">
                    {getAppropriateLocalization(karmaPoint.comment)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        {/each}
      {/if}

      <!-- Infinite scroll sentinel element -->
      {#if isHasMoreKarma}
        <div bind:this={sentinelElement} class="text-center py-3">
          {#if isLoadingMore}
            <div class="spinner-border spinner-border-sm" role="status">
              <span class="visually-hidden">{t.loadingMore}</span>
            </div>
            <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
          {/if}
        </div>
      {/if}

      {#if error}
        <div class="alert alert-danger" role="alert">
          {error}
        </div>
      {/if}
    {/if}
  </div>

  <!-- Karma Change Modal -->
  {#if showModal}
    <div
      class="modal fade show d-block"
      tabindex="-1"
      style="background-color: rgba(0,0,0,0.5);"
      role="dialog"
      aria-modal="true"
      onclick={(e) => e.target === e.currentTarget && closeModal()}
      onkeydown={(e) => e.key === "Enter" && e.target === e.currentTarget && closeModal()}
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{t.karmaModalTitle}</h5>
            <button type="button" class="btn-close" onclick={closeModal} aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <!-- Karma type selection -->
            <div class="mb-3">
              <div class="btn-group w-100" role="group">
                <input
                  type="radio"
                  class="btn-check"
                  name="karmaType"
                  id="giveKarma"
                  bind:group={selectedKarmaType}
                  value={1}
                />
                <label class="btn btn-outline-success" for="giveKarma">
                  <i class="bi bi-plus-lg me-1"></i>
                  {t.giveKarma}
                </label>

                <input
                  type="radio"
                  class="btn-check"
                  name="karmaType"
                  id="takeKarma"
                  bind:group={selectedKarmaType}
                  value={-1}
                />
                <label class="btn btn-outline-danger" for="takeKarma">
                  <i class="bi bi-dash-lg me-1"></i>
                  {t.takeKarma}
                </label>
              </div>
            </div>

            <!-- Comment textarea -->
            <LocalizedTextarea
              {locale}
              id="karmaComment"
              label={t.comment}
              placeholder={t.commentPlaceholder}
              rows={3}
              required
              bind:value={comment}
            />

            <!-- Error message -->
            {#if submitError}
              <div class="alert alert-danger" role="alert">
                {submitError}
              </div>
            {/if}

            <!-- Success message -->
            {#if submitSuccess}
              <div class="alert alert-success" role="alert">
                {t.success}
              </div>
            {/if}
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              onclick={closeModal}
              disabled={isSubmitting}
            >
              {t.cancel}
            </button>
            <button
              type="button"
              class="btn btn-primary"
              onclick={handleSubmitKarma}
              disabled={isSubmitting ||
                comment.length === 0 ||
                !comment.some((c) => c.value.trim())}
            >
              {#if isSubmitting}
                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                {t.submitting}
              {:else}
                {t.submit}
              {/if}
            </button>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .card {
    transition: transform 0.2s ease-in-out;
  }

  .card:hover {
    transform: translateY(-2px);
  }

  .badge {
    font-weight: bold;
  }
</style>
