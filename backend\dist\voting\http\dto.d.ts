import { z } from "zod";
import { Types } from "@commune/api";
export declare const votesRequired: z.Z<PERSON>;
export type Voting = Types.Infer<typeof Voting>;
export declare const Voting: z.ZodObject<{
    id: z.ZodString;
    votesRequired: z.<PERSON>;
    endsAt: z.<PERSON><z.Z<PERSON><[z.<PERSON>od<PERSON>, z.ZodS<PERSON>, z.Zod<PERSON>ate]>, z.ZodDate>;
    title: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.<PERSON>ype<PERSON>, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.Zod<PERSON>tring;
    }, "strip", z.<PERSON>, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    options: z.<PERSON><z.ZodObject<{
        id: z.ZodString;
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }, {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    options: {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: Date;
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    options: {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
}>;
export declare const Votings: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    votesRequired: z.ZodNumber;
    endsAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    options: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }, {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    options: {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: Date;
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    options: {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
}>, "many">;
export type CreateVoting = Types.Infer<typeof CreateVoting>;
export declare const CreateVoting: z.ZodObject<{
    votesRequired: z.ZodNumber;
    endsAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    options: z.ZodArray<z.ZodObject<{
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }, {
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    options: {
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: Date;
}, {
    options: {
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
}>;
