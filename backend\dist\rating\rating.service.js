"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RatingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RatingService = void 0;
const common_1 = require("@nestjs/common");
const utils_1 = require("../utils");
const prisma_service_1 = require("../prisma/prisma.service");
const errors_1 = require("../common/errors");
let RatingService = RatingService_1 = class RatingService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(RatingService_1.name);
    }
    async getUserSummary(userId) {
        const [rating, karma, rate] = await Promise.all([
            this.prisma.userRating.aggregate({
                where: {
                    targetUserId: userId,
                },
                _sum: {
                    value: true,
                },
            }),
            this.prisma.userKarmaGivenPoint.aggregate({
                where: {
                    targetUserId: userId,
                },
                _sum: {
                    quantity: true,
                },
            }),
            this.prisma.userFeedback.aggregate({
                where: {
                    targetUserId: userId,
                },
                _avg: {
                    value: true,
                },
            }),
        ]);
        return {
            rating: rating._sum.value ?? 0,
            karma: karma._sum.quantity ?? 0,
            rate: rate._avg.value,
        };
    }
    async getKarmaPoints(userId, pagination) {
        return await this.prisma.userKarmaGivenPoint.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                targetUserId: userId,
            },
            include: {
                comment: true,
                sourceUser: {
                    include: {
                        name: true,
                        images: {
                            take: 1,
                        },
                    },
                },
            },
        });
    }
    async spendKarmaPoint(data, user) {
        if (!user.isAdmin) {
            if (user.id !== data.sourceUserId) {
                throw new common_1.ForbiddenException((0, errors_1.getError)("must_be_admin_or_source_user"));
            }
            if (data.sourceUserId === data.targetUserId) {
                throw new common_1.ForbiddenException((0, errors_1.getError)("source_and_target_users_must_differ"));
            }
            if (data.quantity !== 1 && data.quantity !== -1) {
                throw new common_1.ForbiddenException((0, errors_1.getError)("must_be_admin_or_use_only_one_point"));
            }
        }
        const id = await this.prisma.$transaction(async (trx) => {
            let userKarmaSpendablePointId = null;
            let availablePoints = Number.MAX_SAFE_INTEGER;
            if (!user.isAdmin) {
                const userKarmaSpendablePoint = await trx.userKarmaSpendablePoint.findUnique({
                    where: {
                        userId: data.sourceUserId,
                    },
                });
                if (!userKarmaSpendablePoint) {
                    throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_have_at_least_one_spendable_point"));
                }
                userKarmaSpendablePointId = userKarmaSpendablePoint.id;
                availablePoints = userKarmaSpendablePoint.points;
            }
            if (availablePoints < data.quantity) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_have_at_least_one_spendable_point"));
            }
            const { id } = await trx.userKarmaGivenPoint.create({
                data: {
                    sourceUserId: data.sourceUserId,
                    targetUserId: data.targetUserId,
                    quantity: data.quantity,
                    comment: {
                        create: (0, utils_1.toPrismaLocalizations)(data.comment, "comment"),
                    },
                },
            });
            if (userKarmaSpendablePointId) {
                await trx.userKarmaSpendablePoint.update({
                    where: {
                        id: userKarmaSpendablePointId,
                    },
                    data: {
                        points: {
                            decrement: data.quantity,
                        },
                    },
                });
            }
            return id;
        });
        return { id };
    }
    async getUserFeedbacks(userId, pagination) {
        return await this.prisma.userFeedback.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                targetUserId: userId,
            },
            include: {
                text: true,
                sourceUser: {
                    include: {
                        name: true,
                        images: {
                            take: 1,
                        },
                    },
                },
            },
        });
    }
    async createUserFeedback(data, user) {
        if (!user.isAdmin) {
            if (user.id !== data.sourceUserId) {
                throw new common_1.ForbiddenException((0, errors_1.getError)("must_be_admin_or_source_user"));
            }
            if (data.sourceUserId === data.targetUserId) {
                throw new common_1.ForbiddenException((0, errors_1.getError)("source_and_target_users_must_differ"));
            }
        }
        const { id } = await this.prisma.userFeedback.create({
            data: {
                sourceUserId: data.sourceUserId,
                targetUserId: data.targetUserId,
                value: data.value,
                isAnonymous: data.isAnonymous,
                text: {
                    create: (0, utils_1.toPrismaLocalizations)(data.text, "text"),
                },
            },
        });
        return { id };
    }
    async upsertRelativeUserRating(data, prisma = this.prisma) {
        await prisma.userRating.upsert({
            where: {
                sourceUserId_targetUserId_entityType_entityId: {
                    sourceUserId: data.sourceUserId,
                    targetUserId: data.targetUserId,
                    entityType: data.entityType,
                    entityId: data.entityId,
                },
            },
            create: {
                sourceUserId: data.sourceUserId,
                targetUserId: data.targetUserId,
                entityType: data.entityType,
                entityId: data.entityId,
                value: data.value,
            },
            update: {
                value: data.value,
            },
        });
    }
    async deleteRelativeUserRating(data, prisma = this.prisma) {
        await prisma.userRating.delete({
            where: {
                sourceUserId_targetUserId_entityType_entityId: {
                    sourceUserId: data.sourceUserId,
                    targetUserId: data.targetUserId,
                    entityType: data.entityType,
                    entityId: data.entityId,
                },
            },
        });
    }
};
exports.RatingService = RatingService;
exports.RatingService = RatingService = RatingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], RatingService);
//# sourceMappingURL=rating.service.js.map