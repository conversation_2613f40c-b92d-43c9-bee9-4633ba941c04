<script lang="ts">
  import type { Common } from "@commune/api";

  import boostyIcon from "./boosty-icon.svg";
  // import pikabuIcon from "./pikabu-icon.svg";

  interface Props {
    locale: Common.LocalizationLocale;
    toLocaleHref: (href: string) => string;
  }

  const i18n = {
    ru: {
      logoAlt: "Логотип Цифрового Сообщества «Коммуна»",
      title: "О нас",
      about:
        "Цифровое Сообщество «Коммуна» - где традиции встречаются с инновациями. Мы строим жизнь в сообществе, которое поддерживает сотрудничество, рост и значимые связи.",
      social: {
        telegram: "https://t.me/ds_commune_ru",
      },
      links: {
        title: "Cсылк<PERSON>",
        reactor: "Реактор",
        communes: "Коммуны",
        users: "Пользователи",
        newCalendar: "Новый календарь",
        newEnglish: "Новый английский",
        theLaw: "Право",
        rules: "Правила",
      },
      contactUs: {
        title: "Связаться с нами",
      },
      legal: {
        disclaimer: "Цифровое Сообщество «Коммуна». Все права защищены.",
      },
    },

    en: {
      logoAlt: "Digital Society «Commune» Logo",
      title: "About Us",
      about:
        "Digital Society «Commune» - where tradition meets innovation. We're building a vibrant community dedicated to fostering collaboration, shared growth, and meaningful connections.",
      social: {
        telegram: "https://t.me/ds_commune_en",
      },
      links: {
        title: "Quick Links",
        reactor: "Reactor",
        communes: "Communes",
        users: "Users",
        newCalendar: "New Calendar",
        newEnglish: "New English",
        theLaw: "The Law",
        rules: "Rules",
      },
      contactUs: {
        title: "Contact Us",
      },
      legal: {
        disclaimer: "Digital Society «Commune». All rights reserved.",
      },
    },
  };

  const { locale, toLocaleHref }: Props = $props();

  const t = $derived(i18n[locale]);

  const contactUsEmail = import.meta.env.PUBLIC_FOOTER_CONTACT_EMAIL || "<EMAIL>";
</script>

<footer class="footer">
  <div class="container">
    <div class="row gy-4">
      <div class="col-lg-4 col-md-6">
        <div class="about-section">
          <a href={toLocaleHref("/")} class="d-inline-block mb-3">
            <img
              src="/images/full-v3-transparent.svg"
              alt={t.logoAlt}
              width={80}
              height={80}
              class="footer-logo"
            />
          </a>
          <h5 class="fw-bold mb-3">{t.title}</h5>
          <p class="text-muted">{t.about}</p>
          <div class="social-icons">
            <a
              href={t.social.telegram}
              class="social-icon"
              aria-label="Telegram"
              target="_blank"
              rel="noopener noreferrer"
            >
              <i class="bi bi-telegram"></i>
            </a>
            <!-- <a
              href="https://pikabu.ru/ds_commune"
              class="social-icon"
              aria-label="Pikabu"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img src={pikabuIcon} alt="Pikabu" width={24} height={24} />
            </a> -->
            <a
              href="https://github.com/ds-commune"
              class="social-icon"
              aria-label="GitHub"
              target="_blank"
              rel="noopener noreferrer"
            >
              <i class="bi bi-github"></i>
            </a>
            <a
              href="https://boosty.to/ds.commune"
              class="social-icon"
              aria-label="Boosty"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img src={boostyIcon} alt="Boosty" width={24} height={24} />
            </a>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6">
        <h5 class="fw-bold mb-3">{t.links.title}</h5>
        <ul class="list-unstyled quick-links">
          <li>
            <a href={toLocaleHref("/the-law")}>{t.links.theLaw}</a>
          </li>
          <li>
            <a href={toLocaleHref("/rules")}>{t.links.rules}</a>
          </li>
          <li>
            <a href={toLocaleHref("/new-english")}>{t.links.newEnglish}</a>
          </li>
          <li>
            <a href={toLocaleHref("/new-calendar")}>{t.links.newCalendar}</a>
          </li>
          <li>
            <a href={toLocaleHref("/reactor")}>{t.links.reactor}</a>
          </li>
          <li>
            <a href={toLocaleHref("/users")}>{t.links.users}</a>
          </li>
          <li>
            <a href={toLocaleHref("/communes")}>{t.links.communes}</a>
          </li>
        </ul>
      </div>
      <div class="col-lg-4 col-md-12">
        <h5 class="fw-bold mb-3">{t.contactUs.title}</h5>
        <ul class="list-unstyled contact-info">
          <li>
            <i class="bi bi-envelope"></i>
            <a href={`mailto:${contactUsEmail}`} target="_blank" rel="noopener noreferrer"
              >{contactUsEmail}</a
            >
          </li>
        </ul>
      </div>
    </div>

    <hr class="divider" />

    <div class="copyright">
      <p>{new Date().getFullYear()} {t.legal.disclaimer}</p>
    </div>
  </div>
</footer>

<style>
  .footer {
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 3rem;
    padding-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 -10px 20px rgba(0, 0, 0, 0.03);
  }

  .footer::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, #0d6efd, #6610f2, #6f42c1);
    opacity: 0.7;
  }

  .footer::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23000000' fill-opacity='0.02' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
    pointer-events: none;
  }

  .footer-logo {
    max-width: 80px;
    height: auto;
    margin-bottom: 1rem;
  }

  .about-section {
    padding-right: 2rem;
  }

  .quick-links li {
    margin-bottom: 0.5rem;
    transition: transform 0.2s ease;
  }

  .quick-links li:hover {
    transform: translateX(5px);
  }

  .quick-links a {
    color: #495057;
    transition: color 0.2s ease;
    display: inline-flex;
    align-items: center;
  }

  .quick-links a:hover {
    color: #0d6efd;
    text-decoration: none;
  }

  .quick-links a::before {
    content: "›";
    margin-right: 0.5rem;
    font-size: 1.2rem;
    line-height: 1;
    opacity: 0.7;
  }

  .contact-info li {
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
  }

  .contact-info li:last-child {
    border-bottom: none;
  }

  .contact-info li:hover {
    transform: translateX(5px);
  }

  .contact-info i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    color: #0d6efd;
    width: 20px;
    text-align: center;
    transition: transform 0.3s ease;
  }

  .contact-info li:hover i {
    transform: scale(1.2);
  }

  .social-icons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    font-size: 1.1rem;
  }

  .social-icon:hover {
    background-color: #0d6efd;
    color: white;
    transform: translateY(-3px) rotate(8deg);
    box-shadow: 0 6px 12px rgba(13, 110, 253, 0.2);
  }

  .divider {
    margin: 1rem 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1), transparent);
    border: none;
  }

  .copyright {
    font-size: 0.9rem;
    color: #6c757d;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    padding-top: 0.5rem;
    position: relative;
  }

  .copyright::before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background: linear-gradient(to right, #0d6efd, #6610f2);
    border-radius: 2px;
  }

  .copyright p {
    margin-bottom: 0;
    position: relative;
    padding-left: 1.5rem;
    text-align: center;
  }

  .copyright p::before {
    content: "©";
    position: absolute;
    left: 0;
    top: 0;
    font-size: 1rem;
    color: #0d6efd;
  }

  @media (max-width: 767.98px) {
    .footer {
      padding-top: 2rem;
      text-align: center;
    }

    .about-section {
      padding-right: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .footer-logo {
      margin: 0 auto 1rem;
    }

    .quick-links {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .quick-links li {
      text-align: center;
    }

    .quick-links a {
      justify-content: center;
    }

    .quick-links a::before {
      position: relative;
      margin-right: 0.5rem;
    }

    .contact-info {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .contact-info li {
      justify-content: center;
    }

    .contact-info i {
      display: inline-block;
      margin-right: 0.75rem;
    }

    .copyright {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
      justify-content: center;
    }

    .copyright p {
      padding-left: 0;
    }

    .copyright p::before {
      position: static;
      margin-right: 0.25rem;
    }

    .social-icons {
      justify-content: center;
    }

    .divider {
      margin: 0;
    }
  }
</style>
