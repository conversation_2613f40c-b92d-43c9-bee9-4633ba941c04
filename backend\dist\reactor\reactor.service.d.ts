import { ReactorRatingType } from "@prisma/client";
import { PrismaService } from "src/prisma/prisma.service";
import { CurrentUser } from "src/auth/types";
import { ReactorLensService } from "./lens/lens.service";
import { Common, Reactor } from "@commune/api";
import { RatingService } from "src/rating/rating.service";
import { MinioService } from "src/minio/minio.service";
export type Post = {
    id: string;
    author: {
        id: string;
        name: Common.Localization[];
        avatar: string | null;
    };
    rating: {
        likes: number;
        dislikes: number;
        status: ReactorRatingType | null;
    };
    usefulness: {
        value: number | null;
        count: number;
        totalValue: number;
    };
    title: Common.Localization[];
    body: Common.Localization[];
    tags: string[];
    createdAt: Date;
    updatedAt: Date;
};
export type PostsResponse = {
    items: Post[];
    total: number;
};
export type Comment = {
    id: string;
    path: string;
    internalNumber: number;
    author: {
        id: string;
        name: Common.Localization[];
        avatar: string | null;
    } | null;
    isAnonymous: boolean;
    anonimityReason: string | null;
    rating: {
        likes: number;
        dislikes: number;
        status: ReactorRatingType | null;
    };
    body: Common.Localization[] | null;
    childrenCount: number;
    deleteReason: string | null;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
};
export type CommentsResponse = {
    items: Comment[];
    total: number;
};
export declare class ReactorService {
    private readonly prisma;
    private readonly minioService;
    private readonly lensService;
    private readonly ratingService;
    private readonly logger;
    constructor(prisma: PrismaService, minioService: MinioService, lensService: ReactorLensService, ratingService: RatingService);
    getPosts(pagination: {
        page: number;
        size: number;
    } | undefined, user: CurrentUser): Promise<PostsResponse>;
    getPost(id: string, user: CurrentUser): Promise<{
        id: string;
        author: {
            id: string;
            name: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
                value: string;
            }[];
            avatar: string | null;
        };
        rating: {
            likes: number;
            dislikes: number;
            status: import("@prisma/client").$Enums.ReactorRatingType | null;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number;
        };
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        body: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        tags: string[];
        createdAt: Date;
        updatedAt: Date;
    }>;
    createPost(dto: {
        title: Common.Localization[];
        body: Common.Localization[];
        tags: string[];
    }, user: CurrentUser): Promise<{
        id: string;
    }>;
    updatePost(id: string, dto: Partial<{
        title: Common.Localization[];
        body: Common.Localization[];
        tags: string[];
    }>, user: CurrentUser): Promise<boolean>;
    updatePostRating(id: string, dto: {
        type: ReactorRatingType;
    }, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import("@prisma/client").$Enums.ReactorRatingType | null;
    }>;
    updatePostUsefulness(id: string, dto: {
        value: number | null;
    }, user: CurrentUser): Promise<{
        count: number;
        totalValue: number | null;
        value: number | null;
    }>;
    deletePost(id: string, dto: {
        reason: string | null;
    }, user: CurrentUser): Promise<boolean>;
    createMockPost(): Promise<{
        id: string;
        authorId: string;
        hubId: string | null;
        communityId: string | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        deleteReason: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    getPosts2(lensId: string | null, pagination: Common.Pagination, user: CurrentUser): Promise<PostsResponse>;
    getComments(dto: {
        entityType: "post" | "comment";
        entityId: string;
    }, user: CurrentUser): Promise<CommentsResponse>;
    getComment(dto: {
        id: string;
    }, user: CurrentUser): Promise<Comment>;
    private getNextCommentInternalNumber;
    createComment(dto: {
        entityType: "post" | "comment";
        entityId: string;
        body: Common.Localization[];
    }, user: CurrentUser): Promise<{
        id: string;
    }>;
    private createPostComment;
    private createCommentComment;
    updateComment(id: string, dto: Partial<{
        body: Common.Localization[];
    }>, user: CurrentUser): Promise<boolean>;
    updateCommentRating(id: string, dto: {
        type: ReactorRatingType;
    }, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import("@prisma/client").$Enums.ReactorRatingType | null;
    }>;
    anonimifyComment(id: string, dto: {
        reason: string | null;
    }, user: CurrentUser): Promise<boolean>;
    deleteComment(id: string, dto: {
        reason: string | null;
    }, user: CurrentUser): Promise<boolean>;
    getLenses(user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: string;
        userId: string;
        code: string;
        sql: string;
    }[]>;
    private generateLensSql;
    createLens(dto: {
        name: string;
        code: string;
    }, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateLens(id: string, dto: {
        name?: string;
        code?: string;
    }, user: CurrentUser): Promise<boolean>;
    deleteLens(id: string, user: CurrentUser): Promise<void>;
    getHubs(data: {
        input: Reactor.GetHubsInput;
        pagination: Common.Pagination;
    }, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        headUser: {
            name: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
                value: string;
            }[];
            images: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
        };
    }[]>;
    createHub(input: Reactor.CreateHubInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        headUserId: string;
        imageId: string | null;
    }>;
    updateHub(id: string, input: Reactor.UpdateHubInput, user: CurrentUser): Promise<void>;
    updateHubImage(id: string, file: Express.Multer.File, user: CurrentUser): Promise<void>;
    deleteHubImage(id: string, user: CurrentUser): Promise<void>;
    getCommunities(data: {
        input: Reactor.GetCommunitiesInput;
        pagination: Common.Pagination;
    }, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        hub: ({
            name: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
                value: string;
            }[];
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            headUserId: string;
            imageId: string | null;
        }) | null;
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        headUser: {
            name: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
                value: string;
            }[];
            images: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
        };
    }[]>;
    createCommunity(input: Reactor.CreateCommunityInput, user: CurrentUser): Promise<{
        id: string;
        hubId: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        headUserId: string;
        imageId: string | null;
    }>;
    updateCommunity(id: string, input: Reactor.UpdateCommunityInput, user: CurrentUser): Promise<void>;
    updateCommunityImage(id: string, file: Express.Multer.File, user: CurrentUser): Promise<void>;
    deleteCommunityImage(id: string, user: CurrentUser): Promise<void>;
}
