"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorController = void 0;
const common_1 = require("@nestjs/common");
const zod_1 = require("../../zod");
const current_user_decorator_1 = require("../../auth/http/current-user.decorator");
const session_auth_guard_1 = require("../../auth/http/session-auth.guard");
const reactor_service_1 = require("../reactor.service");
const api_1 = require("@commune/api");
const file_upload_dto_1 = require("../../commune/http/file-upload.dto");
const platform_express_1 = require("@nestjs/platform-express");
let ReactorController = class ReactorController {
    constructor(reactorService) {
        this.reactorService = reactorService;
    }
    async createMockPost() {
        return await this.reactorService.createMockPost();
    }
    async getPosts2() {
        const posts = await this.reactorService.getPosts2(null, {
            page: 1,
            size: 1000,
        }, {
            id: "0",
            email: "<EMAIL>",
            role: "admin",
            isAdmin: true,
        });
        return posts;
    }
    async getPosts(pagination, user) {
        const response = await this.reactorService.getPosts(pagination, user);
        return api_1.Common.parseInput(api_1.Reactor.GetPostsResponseSchema, response);
    }
    async getPost(id, user) {
        const post = await this.reactorService.getPost(id, user);
        return api_1.Common.parseInput(api_1.Reactor.PostSchema, post);
    }
    async createPost(body, user) {
        return await this.reactorService.createPost(body, user);
    }
    async updatePost(id, body, user) {
        return await this.reactorService.updatePost(id, body, user);
    }
    async updatePostRating(id, body, user) {
        const newRating = await this.reactorService.updatePostRating(id, body, user);
        return api_1.Common.parseInput(api_1.Reactor.UpdatePostRatingResponseSchema, newRating);
    }
    async updatePostUsefulness(id, body, user) {
        const newUsefulness = await this.reactorService.updatePostUsefulness(id, body, user);
        return api_1.Common.parseInput(api_1.Reactor.UpdatePostUsefulnessResponseSchema, newUsefulness);
    }
    async deletePost(id, body, user) {
        return await this.reactorService.deletePost(id, body, user);
    }
    async getComments(body, user) {
        const response = await this.reactorService.getComments(body, user);
        return api_1.Common.parseInput(api_1.Reactor.GetCommentsResponseSchema, response);
    }
    async getComment(id, user) {
        const comment = await this.reactorService.getComment({ id }, user);
        return api_1.Common.parseInput(api_1.Reactor.CommentSchema, comment);
    }
    async createComment(body, user) {
        return await this.reactorService.createComment(body, user);
    }
    async updateComment(id, body, user) {
        return await this.reactorService.updateComment(id, body, user);
    }
    async updateCommentRating(id, body, user) {
        const newRating = await this.reactorService.updateCommentRating(id, body, user);
        return api_1.Common.parseInput(api_1.Reactor.UpdateCommentRatingResponseSchema, newRating);
    }
    async anonimifyComment(id, body, user) {
        return await this.reactorService.anonimifyComment(id, body, user);
    }
    async deleteComment(id, body, user) {
        return await this.reactorService.deleteComment(id, body, user);
    }
    async getLenses(user) {
        return await this.reactorService.getLenses(user);
    }
    async createLens(body, user) {
        return await this.reactorService.createLens(body, user);
    }
    async updateLens(id, body, user) {
        return await this.reactorService.updateLens(id, body, user);
    }
    async deleteLens(id, user) {
        return await this.reactorService.deleteLens(id, user);
    }
    async getHubs(pagination, body, user) {
        const hubs = await this.reactorService.getHubs({
            input: body,
            pagination,
        }, user);
        return api_1.Common.parseInput(api_1.Reactor.GetHubsOutputSchema, hubs);
    }
    async createHub(body, user) {
        const hub = await this.reactorService.createHub(body, user);
        return api_1.Common.parseInput(api_1.Common.ObjectWithIdSchema, hub);
    }
    async updateHub(id, body, user) {
        await this.reactorService.updateHub(id, body, user);
    }
    async updateHubImage(id, user, file) {
        await this.reactorService.updateHubImage(id, file, user);
    }
    async deleteHubImage(id, user) {
        await this.reactorService.deleteHubImage(id, user);
    }
    async getCommunities(pagination, body, user) {
        const communities = await this.reactorService.getCommunities({
            input: body,
            pagination,
        }, user);
        return api_1.Common.parseInput(api_1.Reactor.GetCommunitiesOutputSchema, communities);
    }
    async createCommunity(body, user) {
        const community = await this.reactorService.createCommunity(body, user);
        return api_1.Common.parseInput(api_1.Common.ObjectWithIdSchema, community);
    }
    async updateCommunity(id, body, user) {
        await this.reactorService.updateCommunity(id, body, user);
    }
    async updateCommunityImage(id, user, file) {
        await this.reactorService.updateCommunityImage(id, file, user);
    }
    async deleteCommunityImage(id, user) {
        await this.reactorService.deleteCommunityImage(id, user);
    }
};
exports.ReactorController = ReactorController;
__decorate([
    (0, common_1.Post)("post2"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createMockPost", null);
__decorate([
    (0, common_1.Get)("post2"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getPosts2", null);
__decorate([
    (0, common_1.Get)("post"),
    __param(0, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getPosts", null);
__decorate([
    (0, common_1.Get)("post/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getPost", null);
__decorate([
    (0, common_1.Put)("post"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.CreatePostRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createPost", null);
__decorate([
    (0, common_1.Put)("post/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdatePostRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updatePost", null);
__decorate([
    (0, common_1.Post)("post/:id/rating"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdatePostRatingRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updatePostRating", null);
__decorate([
    (0, common_1.Post)("post/:id/usefulness"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdatePostUsefulnessRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updatePostUsefulness", null);
__decorate([
    (0, common_1.Delete)("post/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.DeletePostRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deletePost", null);
__decorate([
    (0, common_1.Get)("comment"),
    __param(0, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Reactor.GetCommentsRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getComments", null);
__decorate([
    (0, common_1.Get)("comment/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getComment", null);
__decorate([
    (0, common_1.Put)("comment"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.CreateCommentRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createComment", null);
__decorate([
    (0, common_1.Put)("comment/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdateCommentRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateComment", null);
__decorate([
    (0, common_1.Put)("comment/:id/rating"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdateCommentRatingRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateCommentRating", null);
__decorate([
    (0, common_1.Put)("comment/:id/anonimify"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.AnonimifyCommentRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "anonimifyComment", null);
__decorate([
    (0, common_1.Delete)("comment/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.DeleteCommentRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deleteComment", null);
__decorate([
    (0, common_1.Get)("lens"),
    __param(0, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getLenses", null);
__decorate([
    (0, common_1.Put)("lens"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.CreateLensRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createLens", null);
__decorate([
    (0, common_1.Put)("lens/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdateLensRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateLens", null);
__decorate([
    (0, common_1.Delete)("lens/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deleteLens", null);
__decorate([
    (0, common_1.Get)("hub"),
    __param(0, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __param(1, (0, common_1.Query)("body", new zod_1.ZodPipe(api_1.Common.JsonStringToObject(api_1.Reactor.GetHubsInputSchema.shape)))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getHubs", null);
__decorate([
    (0, common_1.Post)("hub"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.CreateHubInputSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createHub", null);
__decorate([
    (0, common_1.Put)("hub/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdateHubInputSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateHub", null);
__decorate([
    (0, common_1.Put)("hub/:id/image"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("image")),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: file_upload_dto_1.MAX_FILE_SIZE }),
            new common_1.FileTypeValidator({
                fileType: file_upload_dto_1.ALLOWED_FILE_TYPES.join("|"),
            }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateHubImage", null);
__decorate([
    (0, common_1.Delete)("hub/:id/image"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deleteHubImage", null);
__decorate([
    (0, common_1.Get)("community"),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __param(1, (0, common_1.Query)("body", new zod_1.ZodPipe(api_1.Common.JsonStringToObject(api_1.Reactor.GetCommunitiesInputSchema.shape)))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getCommunities", null);
__decorate([
    (0, common_1.Post)("community"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.CreateCommunityInputSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createCommunity", null);
__decorate([
    (0, common_1.Put)("community/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdateCommunityInputSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateCommunity", null);
__decorate([
    (0, common_1.Put)("community/:id/image"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("image")),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: file_upload_dto_1.MAX_FILE_SIZE }),
            new common_1.FileTypeValidator({
                fileType: file_upload_dto_1.ALLOWED_FILE_TYPES.join("|"),
            }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateCommunityImage", null);
__decorate([
    (0, common_1.Delete)("community/:id/image"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deleteCommunityImage", null);
exports.ReactorController = ReactorController = __decorate([
    (0, common_1.Controller)("reactor"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __metadata("design:paramtypes", [reactor_service_1.ReactorService])
], ReactorController);
//# sourceMappingURL=reactor.controller.js.map