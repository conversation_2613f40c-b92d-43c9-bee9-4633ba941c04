// src/routes/api/[...slug]/+server.ts
// A catch‑all proxy that forwards any /api/* request to your external API backend.

import type { RequestEvent } from "@sveltejs/kit";
import { API_URL } from "$env/static/private";

/**
 * Proxy handler: forwards the incoming request to the external API,
 * preserving method, headers, query, and body.
 */
async function proxy(event: RequestEvent) {
  const { request, params, url } = event;

  // Reconstruct the path from the catch‑all slug
  const slugPath = params.slug ?? "";

  // Build the external URL, including any search params
  const externalUrl = new URL(`${API_URL}/${slugPath}`);
  externalUrl.search = url.search;

  // Prepare fetch init
  const init: RequestInit = {
    method: request.method,
    headers: {} as HeadersInit,
    redirect: "manual"
  };

  // Copy headers (except Host)
  for (const [key, value] of request.headers) {
    if (key.toLowerCase() === "host") continue;
    init.headers![key as keyof HeadersInit] = value;
  }

  // Copy body for non‑GET/HEAD
  if (request.method !== "GET" && request.method !== "HEAD") {
    init.body = await request.arrayBuffer();
  }

  // Perform the proxy request
  const response = await fetch(externalUrl.toString(), init);

  // Create a new Response using proxied status, headers, and body
  const headers = new Headers(response.headers);
  // Remove hop-by-hop headers to satisfy spec
  headers.delete("connection");
  headers.delete("keep-alive");
  headers.delete("transfer-encoding");
  headers.delete("upgrade");

  return new Response(response.body, {
    status: response.status,
    headers
  });
}

// Export all HTTP methods to the same proxy handler
export const GET = proxy;
export const POST = proxy;
export const PUT = proxy;
export const PATCH = proxy;
export const DELETE = proxy;
export const OPTIONS = proxy;