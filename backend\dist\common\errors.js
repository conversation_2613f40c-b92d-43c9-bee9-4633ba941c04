"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errors = void 0;
exports.getError = getError;
exports.errors = {
    user_not_found: "User not found",
    user_email_is_busy: "User email is busy",
    refresh_token_invalid: "Refresh token is invalid",
    otp_invalid: "OTP is invalid",
    email_already_exists: "Email already taken",
    post_not_found: "Post not found",
    must_be_admin: "Must be an admin to perform this action",
};
function getError(errorCode, additionalInfo) {
    const error = exports.errors[errorCode];
    const errorMessage = error ?? errorCode;
    return [
        errorCode,
        errorMessage + (additionalInfo ? ` (${additionalInfo})` : ""),
    ];
}
//# sourceMappingURL=errors.js.map