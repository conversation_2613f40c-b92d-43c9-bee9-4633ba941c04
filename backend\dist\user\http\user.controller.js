"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const zod_1 = require("../../zod");
const errors_1 = require("../../common/errors");
const user_service_1 = require("../user.service");
const user_title_service_1 = require("../user-title.service");
const current_user_decorator_1 = require("../../auth/http/current-user.decorator");
const session_auth_guard_1 = require("../../auth/http/session-auth.guard");
const api_1 = require("@commune/api");
const MAX_FILE_SIZE = 5 * 1024 * 1024;
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];
let UserController = class UserController {
    constructor(userService, userTitleService) {
        this.userService = userService;
        this.userTitleService = userTitleService;
    }
    async getUsers(pagination) {
        const users = await this.userService.getMany({}, pagination);
        return api_1.Common.parseInput(api_1.User.UsersSchema, users);
    }
    async getUser(id) {
        const user = await this.userService.getOne(id);
        if (!user) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("user_not_found"));
        }
        return api_1.Common.parseInput(api_1.User.UserSchema, user);
    }
    async updateUser(id, body, currentUser) {
        const user = await this.userService.update(id, body, currentUser);
        return api_1.Common.parseInput(api_1.User.UserSchema, user);
    }
    async uploadUserImage(id, currentUser, file) {
        try {
            await this.userService.canChange(id, currentUser);
            if (!file) {
                throw new common_1.BadRequestException("No file uploaded");
            }
            const image = await this.userService.uploadUserImage(id, file);
            return api_1.Common.parseInput(api_1.Common.ImageSchema, image);
        }
        catch (error) {
            console.error("Error uploading image:", error);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException("Failed to upload image");
        }
    }
    async getUserTitles(id, active, pagination) {
        const userTitles = await this.userTitleService.getMany({
            ownerId: id,
            isActive: active,
        }, pagination);
        return api_1.Common.parseInput(api_1.User.UserTitlesSchema, userTitles);
    }
    async updateUserTitle(titleId, body, user) {
        const userTitle = await this.userTitleService.update(titleId, body, user);
        return api_1.Common.parseInput(api_1.User.UserTitleSchema, userTitle);
    }
    async getUserNote(id, user) {
        const note = await this.userService.getUserNote(id, user);
        return api_1.Common.parseInput(api_1.User.GetUserNoteResponseSchema, {
            text: note?.text ?? null,
        });
    }
    async setUserNote(id, body, user) {
        await this.userService.setUserNote({
            userId: id,
            text: body.text,
        }, user);
        return true;
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUsers", null);
__decorate([
    (0, common_1.Get)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUser", null);
__decorate([
    (0, common_1.Put)(":id"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.User.UpdateUserRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateUser", null);
__decorate([
    (0, common_1.Post)(":id/image"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("image")),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
            new common_1.FileTypeValidator({
                fileType: ALLOWED_FILE_TYPES.join("|"),
            }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "uploadUserImage", null);
__decorate([
    (0, common_1.Get)(":id/title"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Query)("active", new zod_1.ZodPipe(zod_1.z.boolean()))),
    __param(2, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUserTitles", null);
__decorate([
    (0, common_1.Put)(":id/title/:titleId"),
    __param(0, (0, common_1.Param)("titleId", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.User.UpdateUserTitleRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateUserTitle", null);
__decorate([
    (0, common_1.Get)(":id/note"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUserNote", null);
__decorate([
    (0, common_1.Put)(":id/note"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.User.SetUserNoteRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "setUserNote", null);
exports.UserController = UserController = __decorate([
    (0, common_1.Controller)("user"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __metadata("design:paramtypes", [user_service_1.UserService,
        user_title_service_1.UserTitleService])
], UserController);
//# sourceMappingURL=user.controller.js.map