"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const utils_1 = require("../utils");
const base_service_1 = require("../common/base-service");
const prisma_service_1 = require("../prisma/prisma.service");
const errors_1 = require("../common/errors");
const minio_service_1 = require("../minio/minio.service");
let UserService = class UserService extends base_service_1.BaseService {
    constructor(prisma, minioService) {
        super("user");
        this.prisma = prisma;
        this.minioService = minioService;
    }
    async canGet(id, user) {
        await this.getOneOrThrow(id);
        return true;
    }
    async canChange(id, user) {
        await this.getOneOrThrow(id);
        if (user.role !== client_1.UserRole.admin) {
            if (user.id !== id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_user_itself"));
            }
        }
        return true;
    }
    async getByEmail(email) {
        return await this.prisma.user.findUnique({
            where: { email, deletedAt: null },
        });
    }
    async create(data) {
        {
            const user = await this.getByEmail(data.email);
            if (user) {
                throw new common_1.BadRequestException(...(0, errors_1.getError)("user_email_is_busy"));
            }
        }
        const user = await this.prisma.user.create({
            data: {
                referrerId: data.referrerId,
                email: data.email,
            },
        });
        return user;
    }
    async check(ids) {
        return await this._check(ids, this.prisma.user, "user");
    }
    async getOne(id) {
        return await this.prisma.user.findUnique({
            where: { id, deletedAt: null },
            include: {
                titles: true,
                name: true,
                description: true,
                images: {
                    orderBy: {
                        createdAt: "desc",
                    },
                },
            },
        });
    }
    async getOneOrThrow(id) {
        const user = await this.getOne(id);
        if (!user) {
            throw this.createNotFoundException();
        }
        return user;
    }
    async getMany(where, pagination) {
        return await this.prisma.user.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
            include: {
                titles: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }
    async createOne(data) {
        return await this.prisma.user.create({ data });
    }
    async createMany(data) {
        return await this.prisma.user.createMany({ data });
    }
    async updateOne(id, data) {
        return await this.prisma.user.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }
    async update(id, data, user) {
        await this.canChange(id, user);
        return await this.prisma.$transaction(async (trx) => {
            await trx.user.update({
                where: {
                    id,
                },
                data: {
                    name: data.name && {
                        deleteMany: {},
                    },
                    description: data.description && {
                        deleteMany: {},
                    },
                },
            });
            return await trx.user.update({
                where: {
                    id,
                },
                data: {
                    name: {
                        create: data.name?.map((name) => ({
                            key: "name",
                            locale: name.locale,
                            value: name.value,
                        })),
                    },
                    description: {
                        create: data.description?.map((description) => ({
                            key: "description",
                            locale: description.locale,
                            value: description.value,
                        })),
                    },
                },
                include: {
                    images: true,
                    name: true,
                    description: true,
                },
            });
        });
    }
    async uploadUserImage(userId, file) {
        await this.getOneOrThrow(userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            include: { images: true },
        });
        const index = user?.images?.length || 0;
        const imageUrl = await this.minioService.uploadUserImage(file, userId, index);
        const image = await this.prisma.image.create({
            data: {
                url: imageUrl,
            },
        });
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                images: {
                    connect: [{ id: image.id }],
                },
            },
        });
        return image;
    }
    async updateMany(where, data) {
        return await this.prisma.user.updateMany({ where, data });
    }
    async softDeleteOne(id) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }
    async softDeleteMany(where) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }
    async deleteOne(id) {
        return await this.prisma.user.delete({ where: { id } });
    }
    async deleteMany(where) {
        return await this.prisma.user.deleteMany({ where });
    }
    async getUserNote(userId, currentUser) {
        return await this.prisma.userNote.findUnique({
            where: {
                sourceUserId_targetUserId: {
                    sourceUserId: currentUser.id,
                    targetUserId: userId,
                },
            },
        });
    }
    async setUserNote(data, currentUser) {
        if (data.text !== null) {
            await this.prisma.userNote.upsert({
                where: {
                    sourceUserId_targetUserId: {
                        sourceUserId: currentUser.id,
                        targetUserId: data.userId,
                    },
                },
                create: {
                    sourceUserId: currentUser.id,
                    targetUserId: data.userId,
                    text: data.text,
                },
                update: {
                    text: data.text,
                },
            });
        }
        else {
            await this.prisma.userNote.delete({
                where: {
                    sourceUserId_targetUserId: {
                        sourceUserId: currentUser.id,
                        targetUserId: data.userId,
                    },
                },
            });
        }
        return true;
    }
};
exports.UserService = UserService;
exports.UserService = UserService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        minio_service_1.MinioService])
], UserService);
//# sourceMappingURL=user.service.js.map