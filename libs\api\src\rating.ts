import type { Infer } from "./types";

import { z } from "zod";
import { id, LocalizationsSchema } from "./common";
import { AuthorSchema } from "./user";

export const karmaPointQuantity = z.number().int();
export const karmaPointComment = LocalizationsSchema.min(1);

export type GetKarmaPointsResponse = Infer<typeof GetKarmaPointsResponseSchema>;
export const GetKarmaPointsResponseSchema = z.array(
    z.object({
        id,
        author: AuthorSchema,
        quantity: karmaPointQuantity,
        comment: karmaPointComment,
    }),
);

export type SpendKarmaPointRequest = Infer<typeof SpendKarmaPointRequestSchema>;
export const SpendKarmaPointRequestSchema = z.object({
    sourceUserId: id,
    targetUserId: id,
    quantity: karmaPointQuantity,
    comment: karmaPointComment,
});

export const userFeedbackValue = z.number().int().min(0).max(10);
export const userFeedbackText = LocalizationsSchema.min(1);

export type GetUserFeedbacksResponse = Infer<typeof GetUserFeedbacksResponseSchema>;
export const GetUserFeedbacksResponseSchema = z.array(
    z.object({
        id,
        author: AuthorSchema.nullable(),
        isAnonymous: z.boolean(),
        value: userFeedbackValue,
        text: userFeedbackText,
    }),
);

export type CreateUserFeedbackRequest = Infer<typeof CreateUserFeedbackRequestSchema>;
export const CreateUserFeedbackRequestSchema = z.object({
    sourceUserId: id,
    targetUserId: id,
    value: userFeedbackValue,
    isAnonymous: z.boolean(),
    text: userFeedbackText,
});

export type GetUserSummaryResponse = Infer<typeof GetUserSummaryResponseSchema>;
export const GetUserSummaryResponseSchema = z.object({
    rating: z.number().int(),
    karma: z.number().int(),
    rate: z.number().min(0).max(10).nullable(),
});
