import { ConfigService } from "@nestjs/config";
import * as prisma from "@prisma/client";
import { EmailService } from "src/email/email.service";
import { UserService } from "src/user/user.service";
import { EmailOtpService } from "src/email/email-otp.service";
type InfoDto = {
    ipAddress: string | null;
    userAgent: string | null;
};
type OtpDto = InfoDto & {
    email: string;
};
type RegisterDto = InfoDto & {
    referrerId: string | null;
    email: string;
    otp: string;
};
type LoginDto = InfoDto & {
    email: string;
    otp: string;
};
export declare class AuthService {
    private readonly configService;
    private readonly emailService;
    private readonly userService;
    private readonly userOtpService;
    private readonly disableRegisterOtpCheck;
    private readonly disableLoginOtpCheck;
    private readonly instanceName;
    private readonly domain;
    private readonly otpSender;
    constructor(configService: ConfigService, emailService: EmailService, userService: UserService, userOtpService: EmailOtpService);
    protected createSessionUser(user: prisma.User): {
        id: string;
        email: string;
        role: prisma.$Enums.UserRole;
    };
    protected generateOtp(): string;
    otp(dto: OtpDto): Promise<boolean>;
    login(dto: LoginDto): Promise<{
        user: {
            id: string;
            email: string;
            role: prisma.$Enums.UserRole;
        };
    }>;
    register(dto: RegisterDto): Promise<{
        user: {
            id: string;
            email: string;
            role: prisma.$Enums.UserRole;
        };
    }>;
}
export {};
