/*
  Warnings:

  - The values [commune] on the enum `commune_member_type` will be removed. If these variants are still used in the database, this will fail.
  - The primary key for the `_commune_description` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_commune_images` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_commune_name` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_commune_tags` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_merchandise_images` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_merchandise_tags` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_post_description` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_post_images` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_post_tags` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_post_title` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_reactor_comment_body` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_reactor_group_description` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_reactor_group_name` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_reactor_hub_description` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_reactor_hub_name` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_reactor_post_body` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_reactor_post_tags` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_reactor_post_title` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_user_description` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_user_images` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_user_name` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_voting_description` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_voting_images` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_voting_option_description` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_voting_option_title` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_voting_tags` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `_voting_title` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `commune_members` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `communes` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `images` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `localizations` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `merchandises` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `posts` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `reactor_comments` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `reactor_groups` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `reactor_hubs` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `reactor_lenses` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `reactor_post_internal_numbers` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `reactor_posts` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `reactor_ratings` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `reactor_usefulnesses` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tags` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `user_otps` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `user_titles` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `users` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `votes` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `voting_options` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `votings` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "commune_member_type_new" AS ENUM ('user');
ALTER TABLE "commune_members" ALTER COLUMN "actorType" TYPE "commune_member_type_new" USING ("actorType"::text::"commune_member_type_new");
ALTER TYPE "commune_member_type" RENAME TO "commune_member_type_old";
ALTER TYPE "commune_member_type_new" RENAME TO "commune_member_type";
DROP TYPE "commune_member_type_old";
COMMIT;

-- DropForeignKey
ALTER TABLE "_commune_description" DROP CONSTRAINT "_commune_description_A_fkey";

-- DropForeignKey
ALTER TABLE "_commune_description" DROP CONSTRAINT "_commune_description_B_fkey";

-- DropForeignKey
ALTER TABLE "_commune_images" DROP CONSTRAINT "_commune_images_A_fkey";

-- DropForeignKey
ALTER TABLE "_commune_images" DROP CONSTRAINT "_commune_images_B_fkey";

-- DropForeignKey
ALTER TABLE "_commune_name" DROP CONSTRAINT "_commune_name_A_fkey";

-- DropForeignKey
ALTER TABLE "_commune_name" DROP CONSTRAINT "_commune_name_B_fkey";

-- DropForeignKey
ALTER TABLE "_commune_tags" DROP CONSTRAINT "_commune_tags_A_fkey";

-- DropForeignKey
ALTER TABLE "_commune_tags" DROP CONSTRAINT "_commune_tags_B_fkey";

-- DropForeignKey
ALTER TABLE "_merchandise_images" DROP CONSTRAINT "_merchandise_images_A_fkey";

-- DropForeignKey
ALTER TABLE "_merchandise_images" DROP CONSTRAINT "_merchandise_images_B_fkey";

-- DropForeignKey
ALTER TABLE "_merchandise_tags" DROP CONSTRAINT "_merchandise_tags_A_fkey";

-- DropForeignKey
ALTER TABLE "_merchandise_tags" DROP CONSTRAINT "_merchandise_tags_B_fkey";

-- DropForeignKey
ALTER TABLE "_post_description" DROP CONSTRAINT "_post_description_A_fkey";

-- DropForeignKey
ALTER TABLE "_post_description" DROP CONSTRAINT "_post_description_B_fkey";

-- DropForeignKey
ALTER TABLE "_post_images" DROP CONSTRAINT "_post_images_A_fkey";

-- DropForeignKey
ALTER TABLE "_post_images" DROP CONSTRAINT "_post_images_B_fkey";

-- DropForeignKey
ALTER TABLE "_post_tags" DROP CONSTRAINT "_post_tags_A_fkey";

-- DropForeignKey
ALTER TABLE "_post_tags" DROP CONSTRAINT "_post_tags_B_fkey";

-- DropForeignKey
ALTER TABLE "_post_title" DROP CONSTRAINT "_post_title_A_fkey";

-- DropForeignKey
ALTER TABLE "_post_title" DROP CONSTRAINT "_post_title_B_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_comment_body" DROP CONSTRAINT "_reactor_comment_body_A_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_comment_body" DROP CONSTRAINT "_reactor_comment_body_B_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_group_description" DROP CONSTRAINT "_reactor_group_description_A_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_group_description" DROP CONSTRAINT "_reactor_group_description_B_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_group_name" DROP CONSTRAINT "_reactor_group_name_A_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_group_name" DROP CONSTRAINT "_reactor_group_name_B_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_hub_description" DROP CONSTRAINT "_reactor_hub_description_A_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_hub_description" DROP CONSTRAINT "_reactor_hub_description_B_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_hub_name" DROP CONSTRAINT "_reactor_hub_name_A_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_hub_name" DROP CONSTRAINT "_reactor_hub_name_B_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_post_body" DROP CONSTRAINT "_reactor_post_body_A_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_post_body" DROP CONSTRAINT "_reactor_post_body_B_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_post_tags" DROP CONSTRAINT "_reactor_post_tags_A_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_post_tags" DROP CONSTRAINT "_reactor_post_tags_B_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_post_title" DROP CONSTRAINT "_reactor_post_title_A_fkey";

-- DropForeignKey
ALTER TABLE "_reactor_post_title" DROP CONSTRAINT "_reactor_post_title_B_fkey";

-- DropForeignKey
ALTER TABLE "_user_description" DROP CONSTRAINT "_user_description_A_fkey";

-- DropForeignKey
ALTER TABLE "_user_description" DROP CONSTRAINT "_user_description_B_fkey";

-- DropForeignKey
ALTER TABLE "_user_images" DROP CONSTRAINT "_user_images_A_fkey";

-- DropForeignKey
ALTER TABLE "_user_images" DROP CONSTRAINT "_user_images_B_fkey";

-- DropForeignKey
ALTER TABLE "_user_name" DROP CONSTRAINT "_user_name_A_fkey";

-- DropForeignKey
ALTER TABLE "_user_name" DROP CONSTRAINT "_user_name_B_fkey";

-- DropForeignKey
ALTER TABLE "_voting_description" DROP CONSTRAINT "_voting_description_A_fkey";

-- DropForeignKey
ALTER TABLE "_voting_description" DROP CONSTRAINT "_voting_description_B_fkey";

-- DropForeignKey
ALTER TABLE "_voting_images" DROP CONSTRAINT "_voting_images_A_fkey";

-- DropForeignKey
ALTER TABLE "_voting_images" DROP CONSTRAINT "_voting_images_B_fkey";

-- DropForeignKey
ALTER TABLE "_voting_option_description" DROP CONSTRAINT "_voting_option_description_A_fkey";

-- DropForeignKey
ALTER TABLE "_voting_option_description" DROP CONSTRAINT "_voting_option_description_B_fkey";

-- DropForeignKey
ALTER TABLE "_voting_option_title" DROP CONSTRAINT "_voting_option_title_A_fkey";

-- DropForeignKey
ALTER TABLE "_voting_option_title" DROP CONSTRAINT "_voting_option_title_B_fkey";

-- DropForeignKey
ALTER TABLE "_voting_tags" DROP CONSTRAINT "_voting_tags_A_fkey";

-- DropForeignKey
ALTER TABLE "_voting_tags" DROP CONSTRAINT "_voting_tags_B_fkey";

-- DropForeignKey
ALTER TABLE "_voting_title" DROP CONSTRAINT "_voting_title_A_fkey";

-- DropForeignKey
ALTER TABLE "_voting_title" DROP CONSTRAINT "_voting_title_B_fkey";

-- DropForeignKey
ALTER TABLE "commune_members" DROP CONSTRAINT "commune_members_commune_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_comments" DROP CONSTRAINT "reactor_comments_author_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_groups" DROP CONSTRAINT "reactor_groups_head_user_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_groups" DROP CONSTRAINT "reactor_groups_hub_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_hubs" DROP CONSTRAINT "reactor_hubs_head_user_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_lenses" DROP CONSTRAINT "reactor_lenses_user_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_posts" DROP CONSTRAINT "reactor_posts_author_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_posts" DROP CONSTRAINT "reactor_posts_group_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_posts" DROP CONSTRAINT "reactor_posts_hub_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_ratings" DROP CONSTRAINT "reactor_ratings_user_id_fkey";

-- DropForeignKey
ALTER TABLE "reactor_usefulnesses" DROP CONSTRAINT "reactor_usefulnesses_user_id_fkey";

-- DropForeignKey
ALTER TABLE "user_titles" DROP CONSTRAINT "user_titles_owner_id_fkey";

-- DropForeignKey
ALTER TABLE "users" DROP CONSTRAINT "users_referrer_id_fkey";

-- DropForeignKey
ALTER TABLE "votes" DROP CONSTRAINT "votes_option_id_fkey";

-- DropForeignKey
ALTER TABLE "votes" DROP CONSTRAINT "votes_voting_id_fkey";

-- DropForeignKey
ALTER TABLE "voting_options" DROP CONSTRAINT "voting_options_voting_id_fkey";

-- AlterTable
ALTER TABLE "_commune_description" DROP CONSTRAINT "_commune_description_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_commune_description_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_commune_images" DROP CONSTRAINT "_commune_images_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_commune_images_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_commune_name" DROP CONSTRAINT "_commune_name_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_commune_name_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_commune_tags" DROP CONSTRAINT "_commune_tags_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_commune_tags_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_merchandise_images" DROP CONSTRAINT "_merchandise_images_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_merchandise_images_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_merchandise_tags" DROP CONSTRAINT "_merchandise_tags_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_merchandise_tags_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_post_description" DROP CONSTRAINT "_post_description_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_post_description_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_post_images" DROP CONSTRAINT "_post_images_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_post_images_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_post_tags" DROP CONSTRAINT "_post_tags_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_post_tags_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_post_title" DROP CONSTRAINT "_post_title_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_post_title_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_reactor_comment_body" DROP CONSTRAINT "_reactor_comment_body_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_reactor_comment_body_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_reactor_group_description" DROP CONSTRAINT "_reactor_group_description_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_reactor_group_description_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_reactor_group_name" DROP CONSTRAINT "_reactor_group_name_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_reactor_group_name_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_reactor_hub_description" DROP CONSTRAINT "_reactor_hub_description_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_reactor_hub_description_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_reactor_hub_name" DROP CONSTRAINT "_reactor_hub_name_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_reactor_hub_name_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_reactor_post_body" DROP CONSTRAINT "_reactor_post_body_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_reactor_post_body_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_reactor_post_tags" DROP CONSTRAINT "_reactor_post_tags_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_reactor_post_tags_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_reactor_post_title" DROP CONSTRAINT "_reactor_post_title_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_reactor_post_title_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_user_description" DROP CONSTRAINT "_user_description_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_user_description_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_user_images" DROP CONSTRAINT "_user_images_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_user_images_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_user_name" DROP CONSTRAINT "_user_name_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_user_name_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_voting_description" DROP CONSTRAINT "_voting_description_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_voting_description_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_voting_images" DROP CONSTRAINT "_voting_images_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_voting_images_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_voting_option_description" DROP CONSTRAINT "_voting_option_description_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_voting_option_description_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_voting_option_title" DROP CONSTRAINT "_voting_option_title_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_voting_option_title_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_voting_tags" DROP CONSTRAINT "_voting_tags_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_voting_tags_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "_voting_title" DROP CONSTRAINT "_voting_title_AB_pkey",
ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT,
ADD CONSTRAINT "_voting_title_AB_pkey" PRIMARY KEY ("A", "B");

-- AlterTable
ALTER TABLE "commune_members" DROP CONSTRAINT "commune_members_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "commune_id" SET DATA TYPE TEXT,
ALTER COLUMN "actor_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "commune_members_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "communes" DROP CONSTRAINT "communes_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "communes_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "images" DROP CONSTRAINT "images_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "images_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "localizations" DROP CONSTRAINT "localizations_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "localizations_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "merchandises" DROP CONSTRAINT "merchandises_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "merchandises_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "posts" DROP CONSTRAINT "posts_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "posts_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "reactor_comments" DROP CONSTRAINT "reactor_comments_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "author_id" SET DATA TYPE TEXT,
ALTER COLUMN "post_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "reactor_comments_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "reactor_groups" DROP CONSTRAINT "reactor_groups_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "hub_id" SET DATA TYPE TEXT,
ALTER COLUMN "head_user_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "reactor_groups_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "reactor_hubs" DROP CONSTRAINT "reactor_hubs_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "head_user_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "reactor_hubs_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "reactor_lenses" DROP CONSTRAINT "reactor_lenses_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "user_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "reactor_lenses_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "reactor_post_internal_numbers" DROP CONSTRAINT "reactor_post_internal_numbers_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "post_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "reactor_post_internal_numbers_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "reactor_posts" DROP CONSTRAINT "reactor_posts_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "author_id" SET DATA TYPE TEXT,
ALTER COLUMN "group_id" SET DATA TYPE TEXT,
ALTER COLUMN "hub_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "reactor_posts_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "reactor_ratings" DROP CONSTRAINT "reactor_ratings_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "user_id" SET DATA TYPE TEXT,
ALTER COLUMN "entity_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "reactor_ratings_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "reactor_usefulnesses" DROP CONSTRAINT "reactor_usefulnesses_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "user_id" SET DATA TYPE TEXT,
ALTER COLUMN "entity_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "reactor_usefulnesses_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "tags" DROP CONSTRAINT "tags_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "tags_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "user_otps" DROP CONSTRAINT "user_otps_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "user_otps_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "user_titles" DROP CONSTRAINT "user_titles_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "owner_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "user_titles_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "users" DROP CONSTRAINT "users_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "referrer_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "votes" DROP CONSTRAINT "votes_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "actor_id" SET DATA TYPE TEXT,
ALTER COLUMN "voting_id" SET DATA TYPE TEXT,
ALTER COLUMN "option_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "votes_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "voting_options" DROP CONSTRAINT "voting_options_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "voting_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "voting_options_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "votings" DROP CONSTRAINT "votings_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "votings_pkey" PRIMARY KEY ("id");

-- AddForeignKey
ALTER TABLE "commune_members" ADD CONSTRAINT "commune_members_commune_id_fkey" FOREIGN KEY ("commune_id") REFERENCES "communes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_posts" ADD CONSTRAINT "reactor_posts_author_id_fkey" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_posts" ADD CONSTRAINT "reactor_posts_hub_id_fkey" FOREIGN KEY ("hub_id") REFERENCES "reactor_hubs"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_posts" ADD CONSTRAINT "reactor_posts_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "reactor_groups"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_comments" ADD CONSTRAINT "reactor_comments_author_id_fkey" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_ratings" ADD CONSTRAINT "reactor_ratings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_usefulnesses" ADD CONSTRAINT "reactor_usefulnesses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_hubs" ADD CONSTRAINT "reactor_hubs_head_user_id_fkey" FOREIGN KEY ("head_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_groups" ADD CONSTRAINT "reactor_groups_hub_id_fkey" FOREIGN KEY ("hub_id") REFERENCES "reactor_hubs"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_groups" ADD CONSTRAINT "reactor_groups_head_user_id_fkey" FOREIGN KEY ("head_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_lenses" ADD CONSTRAINT "reactor_lenses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_referrer_id_fkey" FOREIGN KEY ("referrer_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_titles" ADD CONSTRAINT "user_titles_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "voting_options" ADD CONSTRAINT "voting_options_voting_id_fkey" FOREIGN KEY ("voting_id") REFERENCES "votings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "votes" ADD CONSTRAINT "votes_voting_id_fkey" FOREIGN KEY ("voting_id") REFERENCES "votings"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "votes" ADD CONSTRAINT "votes_option_id_fkey" FOREIGN KEY ("option_id") REFERENCES "voting_options"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_images" ADD CONSTRAINT "_commune_images_A_fkey" FOREIGN KEY ("A") REFERENCES "communes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_images" ADD CONSTRAINT "_commune_images_B_fkey" FOREIGN KEY ("B") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_tags" ADD CONSTRAINT "_commune_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "communes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_tags" ADD CONSTRAINT "_commune_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_name" ADD CONSTRAINT "_commune_name_A_fkey" FOREIGN KEY ("A") REFERENCES "communes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_name" ADD CONSTRAINT "_commune_name_B_fkey" FOREIGN KEY ("B") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_description" ADD CONSTRAINT "_commune_description_A_fkey" FOREIGN KEY ("A") REFERENCES "communes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_description" ADD CONSTRAINT "_commune_description_B_fkey" FOREIGN KEY ("B") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_images" ADD CONSTRAINT "_user_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_images" ADD CONSTRAINT "_user_images_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_images" ADD CONSTRAINT "_voting_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_images" ADD CONSTRAINT "_voting_images_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_images" ADD CONSTRAINT "_post_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_images" ADD CONSTRAINT "_post_images_B_fkey" FOREIGN KEY ("B") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_images" ADD CONSTRAINT "_merchandise_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_images" ADD CONSTRAINT "_merchandise_images_B_fkey" FOREIGN KEY ("B") REFERENCES "merchandises"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_name" ADD CONSTRAINT "_user_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_name" ADD CONSTRAINT "_user_name_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_description" ADD CONSTRAINT "_user_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_description" ADD CONSTRAINT "_user_description_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_title" ADD CONSTRAINT "_voting_title_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_title" ADD CONSTRAINT "_voting_title_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_description" ADD CONSTRAINT "_voting_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_description" ADD CONSTRAINT "_voting_description_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_title" ADD CONSTRAINT "_voting_option_title_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_title" ADD CONSTRAINT "_voting_option_title_B_fkey" FOREIGN KEY ("B") REFERENCES "voting_options"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_description" ADD CONSTRAINT "_voting_option_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_description" ADD CONSTRAINT "_voting_option_description_B_fkey" FOREIGN KEY ("B") REFERENCES "voting_options"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_title" ADD CONSTRAINT "_post_title_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_title" ADD CONSTRAINT "_post_title_B_fkey" FOREIGN KEY ("B") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_description" ADD CONSTRAINT "_post_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_description" ADD CONSTRAINT "_post_description_B_fkey" FOREIGN KEY ("B") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_title" ADD CONSTRAINT "_reactor_post_title_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_title" ADD CONSTRAINT "_reactor_post_title_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_body" ADD CONSTRAINT "_reactor_post_body_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_body" ADD CONSTRAINT "_reactor_post_body_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_comment_body" ADD CONSTRAINT "_reactor_comment_body_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_comment_body" ADD CONSTRAINT "_reactor_comment_body_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_comments"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_name" ADD CONSTRAINT "_reactor_hub_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_name" ADD CONSTRAINT "_reactor_hub_name_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_hubs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_description" ADD CONSTRAINT "_reactor_hub_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_description" ADD CONSTRAINT "_reactor_hub_description_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_hubs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_group_name" ADD CONSTRAINT "_reactor_group_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_group_name" ADD CONSTRAINT "_reactor_group_name_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_group_description" ADD CONSTRAINT "_reactor_group_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_group_description" ADD CONSTRAINT "_reactor_group_description_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_tags" ADD CONSTRAINT "_merchandise_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "merchandises"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_tags" ADD CONSTRAINT "_merchandise_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_tags" ADD CONSTRAINT "_post_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_tags" ADD CONSTRAINT "_post_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_tags" ADD CONSTRAINT "_reactor_post_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "reactor_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_tags" ADD CONSTRAINT "_reactor_post_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_tags" ADD CONSTRAINT "_voting_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_tags" ADD CONSTRAINT "_voting_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;
