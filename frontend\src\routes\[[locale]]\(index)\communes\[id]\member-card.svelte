<script lang="ts">
  import type { Common, Commune } from "@commune/api";
  import type { GetAppropriateLocalization } from "$lib";

  import { fetchWithAuth } from "$lib";
  import CommuneImageCarousel from "../commune-image-carousel.svelte";

  interface Props {
    id: string;
    actorType: Commune.CommuneMemberType;
    actorId: string;
    name: Common.Localizations;
    isHead: boolean;
    joinedAt: Date;
    communeId: string;
    isCurrentUserHead: boolean;
    isCurrentUserAdmin: boolean;
    locale: Common.LocalizationLocale;
    onMemberRemoved: () => void;
    onHeadTransferred?: () => void;
    images: Common.Images;
    toLocaleHref: (href: string) => string;
    getAppropriateLocalization: GetAppropriateLocalization;
  }

  const i18n = {
    en: {
      head: "Head",
      joined: "Joined",
      remove: "Remove",
      removing: "Removing...",
      transferHead: "Make Head",
      transferring: "Transferring...",
      confirmRemoval: "Confirm Removal",
      confirmRemoveMessage: "Are you sure you want to remove",
      fromCommune: "from this commune?",
      cannotUndo: "This action cannot be undone.",
      confirmTransfer: "Transfer Head Status",
      confirmTransferMessage: "Are you sure you want to transfer head status to",
      transferWarning:
        "This will make them the new head of the commune and remove your head privileges. This action cannot be undone.",
      transferSuccess: "Head status transferred successfully",
      cancel: "Cancel",
      removeMember: "Remove Member",
      transferHeadStatus: "Transfer Head Status",
      errorRemovingMember: "Failed to remove member",
      errorTransferringHead: "Failed to transfer head status",
      errorOccurred: "An error occurred while removing member",
      dateFormatLocale: "en-US",
    },
    ru: {
      head: "Глава",
      joined: "Присоединился",
      remove: "Удалить",
      removing: "Удаление...",
      transferHead: "Сделать главой",
      transferring: "Передача...",
      confirmRemoval: "Подтвердите удаление",
      confirmRemoveMessage: "Вы уверены, что хотите удалить",
      fromCommune: "из этой коммуны?",
      cannotUndo: "Это действие нельзя отменить.",
      confirmTransfer: "Передача статуса главы",
      confirmTransferMessage: "Вы уверены, что хотите передать статус главы пользователю",
      transferWarning:
        "Это сделает их новым главой коммуны и лишит вас привилегий главы. Это действие нельзя отменить.",
      transferSuccess: "Статус главы успешно передан",
      cancel: "Отмена",
      removeMember: "Удалить участника",
      transferHeadStatus: "Передать статус главы",
      errorRemovingMember: "Не удалось удалить участника",
      errorTransferringHead: "Не удалось передать статус главы",
      errorOccurred: "Произошла ошибка при удалении участника",
      dateFormatLocale: "ru-RU",
    },
  };

  const {
    id,
    actorType,
    actorId,
    name,
    isHead,
    joinedAt,
    communeId,
    isCurrentUserHead,
    isCurrentUserAdmin,
    locale,
    onMemberRemoved,
    onHeadTransferred,
    images,
    toLocaleHref,
    getAppropriateLocalization,
  }: Props = $props();

  const t = $derived(i18n[locale]);

  let showConfirmModal = $state(false);
  let showTransferModal = $state(false);
  let isRemoving = $state(false);
  let isTransferring = $state(false);
  let error = $state<string | null>(null);

  const memberName = getAppropriateLocalization(name);
  const profileUrl = actorType === "user" ? `/users/${actorId}` : `/communes/${actorId}`;

  // Format join date
  const formattedDate = $derived(
    joinedAt.toLocaleDateString(t.dateFormatLocale, {
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
  );

  // Don't allow removing the head member
  const canRemove = (isCurrentUserHead || isCurrentUserAdmin) && !isHead;

  // Only current head or admin can transfer head status to other members (not themselves)
  const canTransferHead =
    (isCurrentUserHead || isCurrentUserAdmin) && !isHead && actorType === "user";

  function handleRemoveClick(e: Event) {
    e.preventDefault();
    e.stopPropagation();
    showConfirmModal = true;
  }

  function handleTransferHeadClick(e: Event) {
    e.preventDefault();
    e.stopPropagation();
    showTransferModal = true;
  }

  async function handleConfirmRemove() {
    isRemoving = true;
    error = null;

    try {
      const response = await fetchWithAuth(`/api/commune?id=${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.errorRemovingMember);
      }

      showConfirmModal = false;
      onMemberRemoved();
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isRemoving = false;
    }
  }

  async function handleConfirmTransfer() {
    isTransferring = true;
    error = null;

    try {
      const response = await fetchWithAuth(`/api/commune/${communeId}/transfer-head-status`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          newHeadUserId: actorId,
        }),
      });

      if (!response.ok) {
        throw new Error(`${t.errorTransferringHead}: ${response.statusText}`);
      }

      alert(t.transferSuccess);
      showTransferModal = false;

      // Call the callback if provided, otherwise refresh the page
      if (onHeadTransferred) {
        onHeadTransferred();
      } else {
        window.location.reload();
      }
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorTransferringHead;
      console.error("Error transferring head status:", err);
    } finally {
      isTransferring = false;
    }
  }

  function closeModal() {
    showConfirmModal = false;
  }

  function closeTransferModal() {
    showTransferModal = false;
  }
</script>

<div class={`card h-100 shadow-sm ${isHead ? "head-member" : ""}`}>
  <CommuneImageCarousel {images} communeId={actorId} {locale} />
  <a href={toLocaleHref(profileUrl)} class="text-decoration-none text-black">
    <div class="card-body d-flex flex-column">
      <h5 class="card-title fs-5 text-truncate">
        {memberName}
      </h5>
      <div class="mt-auto d-flex justify-content-between align-items-center">
        <small class="text-muted">
          {t.joined}
          {formattedDate}
        </small>
        <!-- {#if isHead}
          <span class="badge bg-warning text-dark">{t.head}</span>
        {/if} -->
      </div>

      {#if canTransferHead || canRemove}
        <div class="mt-2 d-flex flex-column gap-1">
          {#if canTransferHead}
            <button class="btn btn-outline-warning btn-sm" onclick={handleTransferHeadClick}>
              {t.transferHead}
            </button>
          {/if}
          {#if canRemove}
            <button class="btn btn-outline-danger btn-sm" onclick={handleRemoveClick}>
              {t.remove}
            </button>
          {/if}
        </div>
      {/if}
    </div>
  </a>
</div>

<!-- Confirmation Modal -->
{#if showConfirmModal}
  <!-- svelte-ignore a11y_click_events_have_key_events -->
  <!-- svelte-ignore a11y_no_static_element_interactions -->
  <div
    class="modal fade show"
    style="display: block; background-color: rgba(0,0,0,0.5);"
    tabindex="-1"
    aria-modal="true"
    role="dialog"
    onclick={closeModal}
    onkeydown={(e) => e.key === "Escape" && closeModal()}
  >
    <!-- svelte-ignore a11y_click_events_have_key_events -->
    <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
    <div
      class="modal-dialog modal-dialog-centered"
      role="document"
      onclick={(e) => e.stopPropagation()}
    >
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{t.confirmRemoval}</h5>
          <button type="button" class="btn-close" aria-label="Close" onclick={closeModal}></button>
        </div>
        <div class="modal-body">
          {#if error}
            <div class="alert alert-danger">{error}</div>
          {/if}
          <p>{t.confirmRemoveMessage} <strong>{memberName}</strong> {t.fromCommune}</p>
          <p class="text-muted small">{t.cannotUndo}</p>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            onclick={closeModal}
            disabled={isRemoving}
          >
            {t.cancel}
          </button>
          <button
            type="button"
            class="btn btn-danger"
            onclick={handleConfirmRemove}
            disabled={isRemoving}
          >
            {isRemoving ? t.removing : t.removeMember}
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}

<!-- Transfer Head Status Confirmation Modal -->
{#if showTransferModal}
  <!-- svelte-ignore a11y_click_events_have_key_events -->
  <!-- svelte-ignore a11y_no_static_element_interactions -->
  <div
    class="modal fade show"
    style="display: block; background-color: rgba(0,0,0,0.5);"
    tabindex="-1"
    aria-modal="true"
    role="dialog"
    onclick={closeTransferModal}
    onkeydown={(e) => e.key === "Escape" && closeTransferModal()}
  >
    <!-- svelte-ignore a11y_click_events_have_key_events -->
    <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
    <div
      class="modal-dialog modal-dialog-centered"
      role="document"
      onclick={(e) => e.stopPropagation()}
    >
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{t.confirmTransfer}</h5>
          <button type="button" class="btn-close" aria-label="Close" onclick={closeTransferModal}
          ></button>
        </div>
        <div class="modal-body">
          {#if error}
            <div class="alert alert-danger" role="alert">
              {error}
            </div>
          {/if}
          <p>
            {t.confirmTransferMessage} <strong>{memberName}</strong>?
          </p>
          <div class="alert alert-warning" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {t.transferWarning}
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" onclick={closeTransferModal}>
            {t.cancel}
          </button>
          <button
            type="button"
            class="btn btn-warning"
            onclick={handleConfirmTransfer}
            disabled={isTransferring}
          >
            {#if isTransferring}
              <span class="spinner-border spinner-border-sm me-2" role="status"></span>
              {t.transferring}
            {:else}
              {t.transferHeadStatus}
            {/if}
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .head-member {
    border: 2px solid #ffc107;
    box-shadow: 0 0.25rem 0.75rem rgba(255, 193, 7, 0.15) !important;
  }
</style>
