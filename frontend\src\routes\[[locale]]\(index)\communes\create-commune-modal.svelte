<script lang="ts">
  import type { Common } from "@commune/api";

  import { fetchWithAuth, preventDefault } from "$lib";
  import { LocalizedInput, LocalizedTextarea } from "../components";
  import { Modal } from "$lib/components";

  interface Props {
    locale: Common.LocalizationLocale;
    show: boolean;
    onHide: () => void;
  }

  // Constants for file upload validation
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  const MAX_FILES_COUNT = 10;
  const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];

  const i18n = {
    en: {
      createNewCommune: "Create New Commune",
      communeCreatedSuccess: "Commune created successfully!",
      images: "Images (optional)",
      uploadInfo: "Upload up to 10 images (JPG, PNG, WebP), max 5MB each.",
      name: "Name",
      enterCommuneName: "Enter commune name",
      description: "Description (optional)",
      enterCommuneDescription: "Enter commune description",
      cancel: "Cancel",
      create: "Create",
      creating: "Creating...",
      provideName: "Please provide a name for the commune.",
      tooManyFiles: "You can upload a maximum of {count} images.",
      unsupportedFormat:
        "File '{name}' has an unsupported format. Only JPG, PNG, and WebP are allowed.",
      fileTooLarge: "File '{name}' exceeds the maximum size of 5MB.",
      failedToCreate: "Failed to create commune",
      unexpectedError: "An unexpected error occurred. Please try again.",
    },
    ru: {
      createNewCommune: "Создать новую коммуну",
      communeCreatedSuccess: "Коммуна успешно создана!",
      images: "Изображения (опционально)",
      uploadInfo: "Загрузите до 10 изображений (JPG, PNG, WebP), макс. 5МБ каждое.",
      name: "Название",
      enterCommuneName: "Введите название коммуны",
      description: "Описание (опционально)",
      enterCommuneDescription: "Введите описание коммуны",
      cancel: "Отмена",
      create: "Создать",
      creating: "Создание...",
      provideName: "Пожалуйста, укажите название коммуны.",
      tooManyFiles: "Вы можете загрузить максимум {count} изображений.",
      unsupportedFormat:
        "Файл '{name}' имеет неподдерживаемый формат. Разрешены только JPG, PNG и WebP.",
      fileTooLarge: "Файл '{name}' превышает максимальный размер 5МБ.",
      failedToCreate: "Не удалось создать коммуну",
      unexpectedError: "Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова.",
    },
  };

  const { show, locale, onHide }: Props = $props();

  const t = $derived(i18n[locale]);

  let fileInputRef = $state<HTMLInputElement | null>(null);
  let communeName = $state<Common.Localizations>([]);
  let communeDescription = $state<Common.Localizations>([]);
  let selectedFiles = $state<File[]>([]);
  let previewUrls = $state<string[]>([]);
  let error = $state<string>("");
  let isSubmitting = $state(false);
  let submitSuccess = $state(false);

  function validateFiles(files: File[]): boolean {
    // Check if too many files
    if (files.length > MAX_FILES_COUNT) {
      error = t.tooManyFiles.replace("{count}", MAX_FILES_COUNT.toString());
      return false;
    }

    // Check file types and sizes
    for (const file of files) {
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        error = t.unsupportedFormat.replace("{name}", file.name);
        return false;
      }

      if (file.size > MAX_FILE_SIZE) {
        error = t.fileTooLarge.replace("{name}", file.name);
        return false;
      }
    }

    return true;
  }

  function handleFileChange(e: Event) {
    error = "";
    const input = e.target as HTMLInputElement;
    const files = Array.from(input.files || []);

    if (files.length === 0) return;

    if (!validateFiles(files)) {
      if (fileInputRef) {
        fileInputRef.value = "";
      }
      return;
    }

    // Create preview URLs
    const newPreviewUrls = files.map((file) => URL.createObjectURL(file));

    // Store files and previews
    selectedFiles = files;
    previewUrls = newPreviewUrls;
  }

  function removeFile(index: number) {
    selectedFiles = selectedFiles.filter((_, i) => i !== index);

    // Revoke the URL to prevent memory leaks
    URL.revokeObjectURL(previewUrls[index]);
    previewUrls = previewUrls.filter((_, i) => i !== index);
  }

  function handleClose() {
    // Clean up preview URLs
    previewUrls.forEach((url) => URL.revokeObjectURL(url));

    // Reset form values
    onHide();
    communeName = [];
    communeDescription = [];
    selectedFiles = [];
    previewUrls = [];
    error = "";
    submitSuccess = false;

    if (fileInputRef) {
      fileInputRef.value = "";
    }
  }

  async function handleSubmit() {
    error = "";
    isSubmitting = true;

    try {
      // Validate required fields
      if (!communeName.some((item) => item.value.trim().length)) {
        error = t.provideName;
        isSubmitting = false;
        return;
      }

      // Create FormData for file upload
      const formData = new FormData();

      formData.append(
        "data",
        JSON.stringify({
          name: communeName,
          description: communeDescription,
        }),
      );

      // Add files
      selectedFiles.forEach((file) => {
        formData.append("images", file);
      });

      const response = await fetchWithAuth("/api/commune", {
        method: "POST",
        body: formData,
        // Don't set Content-Type header, browser will set it with boundary for FormData
      });

      if (response.ok) {
        const result = await response.json();

        submitSuccess = true;

        // Close modal after a short delay
        setTimeout(() => {
          handleClose();
          // Reload the page to show the new commune
          window.location.reload();
        }, 1500);
      } else {
        const errorData = await response.json().catch(() => ({ message: t.failedToCreate }));
        error = errorData.message || t.failedToCreate;
      }
    } catch (err) {
      console.error("Error creating commune:", err);
      error = t.unexpectedError;
    } finally {
      isSubmitting = false;
    }
  }
</script>

<Modal
  {show}
  title={t.createNewCommune}
  onClose={handleClose}
  onSubmit={handleSubmit}
  submitText={isSubmitting ? t.creating : t.create}
  cancelText={t.cancel}
  submitDisabled={!communeName.some((item) => item.value.trim().length) || isSubmitting}
  cancelDisabled={isSubmitting}
  {isSubmitting}
>
  {#if submitSuccess}
    <div class="alert alert-success mb-3">
      {t.communeCreatedSuccess}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  <form onsubmit={preventDefault(handleSubmit)}>
    <div class="mb-3">
      <label for="communeImages" class="form-label">{t.images}</label>
      <input
        type="file"
        class="form-control"
        id="communeImages"
        multiple
        accept=".jpg,.jpeg,.png,.webp"
        onchange={handleFileChange}
        bind:this={fileInputRef}
      />
      <div class="form-text text-muted">
        {t.uploadInfo}
      </div>

      {#if previewUrls.length > 0}
        <div class="mt-3 d-flex flex-wrap gap-2">
          {#each previewUrls as url, index}
            <div class="position-relative" style="width: 100px; height: 100px;">
              <img
                src={url}
                alt={`Preview ${index + 1}`}
                class="img-thumbnail"
                style="width: 100%; height: 100%; object-fit: cover;"
              />
              <button
                type="button"
                class="btn btn-danger btn-sm position-absolute top-0 end-0"
                onclick={() => removeFile(index)}
                style="font-size: 0.7rem; padding: 0.1rem 0.3rem;"
              >
                ×
              </button>
            </div>
          {/each}
        </div>
      {/if}
    </div>

    <LocalizedInput
      id="communeName"
      label={t.name}
      placeholder={t.enterCommuneName}
      required={true}
      {locale}
      bind:value={communeName}
    />

    <LocalizedTextarea
      id="communeDescription"
      label={t.description}
      placeholder={t.enterCommuneDescription}
      rows={4}
      {locale}
      bind:value={communeDescription}
    />
  </form>
</Modal>
