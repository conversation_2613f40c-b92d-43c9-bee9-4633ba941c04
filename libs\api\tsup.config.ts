import { defineConfig } from "tsup";

export default defineConfig([
    {
        entry: ["src/index.ts"],
        format: "cjs",
        outDir: "dist",
        splitting: true,
        clean: true,
        sourcemap: true,
        // dts: true,
        skipNodeModulesBundle: true,
        outExtension: () => ({ js: ".cjs" }),
    },
    {
        entry: ["src/index.ts"],
        format: "esm",
        outDir: "dist",
        splitting: true,
        clean: true,
        sourcemap: true,
        skipNodeModulesBundle: true,
        outExtension: () => ({ js: ".mjs" }),

        dts: {
            compilerOptions: {
                composite: false,
            },
        },
    },
]);
