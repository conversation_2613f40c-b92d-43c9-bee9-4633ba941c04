import type { PageLoad } from "./$types";
import type { Auth, Reactor } from "@commune/api";

import { error } from "@sveltejs/kit";
import { handleUnauthorized } from "$lib";

async function fetchCommunity(fetch: typeof globalThis.fetch, communityId: string): Promise<Reactor.GetCommunitiesOutput[0]> {
  const response = await fetch(
    `/api/reactor/community?page=1&size=1&body=${encodeURIComponent(
      JSON.stringify({ ids: [communityId] }),
    )}`,
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch community: ${response.statusText}`);
  }

  const communities: Reactor.GetCommunitiesOutput = await response.json();
  
  if (communities.length === 0) {
    throw error(404, "Community not found");
  }

  return communities[0];
}

async function fetchCurrentUser(fetch: typeof globalThis.fetch): Promise<Auth.GetMeResponse | null> {
  try {
    const response = await fetch("/api/auth/me");
    return response.ok ? await response.json() : null;
  } catch {
    return null;
  }
}

export const load: PageLoad = async ({ fetch, url, params }) => {
  const communityId = params.id;

  if (!communityId) {
    throw error(400, "Community ID is required");
  }

  const [user, community] = await Promise.all([
    fetchCurrentUser(fetch),
    fetchCommunity(fetch, communityId).catch((err) => {
      if (err.status === 404) {
        throw err;
      }
      // For other errors, check if it's an auth issue
      const testResponse = fetch("/api/reactor/community?page=1&size=1");
      testResponse.then((response) => {
        handleUnauthorized(response, url);
      });
      throw err;
    }),
  ]);

  // Check if user can edit this community (admin or head user)
  const canEdit = user && (user.role === "admin" || user.id === community.headUser.id);

  return {
    community,
    user,
    canEdit,
  };
};
