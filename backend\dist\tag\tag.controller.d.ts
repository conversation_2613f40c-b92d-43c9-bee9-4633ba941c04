import { CurrentUser } from "src/auth/types";
import { TagService } from "./tag.service";
import { Common, Tag } from "@commune/api";
export declare class TagController {
    private readonly tagService;
    constructor(tagService: TagService);
    getTags(query: Common.Pagination, input: Tag.GetTagsInput, user: CurrentUser): Promise<{
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        deletedAt?: Date | null | undefined;
    }[]>;
    createTag(input: Tag.CreateTagInput, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateTag(id: string, input: Tag.UpdateTagInput, user: CurrentUser): Promise<void>;
    deleteTag(id: string, user: CurrentUser): Promise<void>;
}
