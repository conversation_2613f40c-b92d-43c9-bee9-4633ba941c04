import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    UseGuards,
} from "@nestjs/common";
import { ZodPipe } from "src/zod";
import { CurrentUser } from "src/auth/types";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { HttpSessionAuthGuard } from "src/auth/http/session-auth.guard";
import { TagService } from "./tag.service";

import { Common, Tag } from "@commune/api";

@Controller("tag")
@UseGuards(HttpSessionAuthGuard)
export class TagController {
    constructor(private readonly tagService: TagService) {}

    @Get()
    async getTags(
        @Query(new ZodPipe(Common.PaginationSchema))
        query: Common.Pagination,
        @Body(new ZodPipe(Tag.GetTagsInputSchema))
        input: Tag.GetTagsInput,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return Common.parseInput(
            Tag.GetTagsOutputSchema,
            await this.tagService.getTags(input, user),
        );
    }

    @Post()
    async createTag(
        @Body(new ZodPipe(Tag.CreateTagInputSchema))
        input: Tag.CreateTagInput,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return Common.parseInput(
            Tag.CreateTagOutputSchema,
            await this.tagService.createTag(input, user),
        );
    }

    @Put(":id")
    async updateTag(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Tag.UpdateTagInputSchema))
        input: Tag.UpdateTagInput,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.tagService.updateTag(id, input, user);
    }

    @Delete(":id")
    async deleteTag(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.tagService.deleteTag(id, user);
    }
}
