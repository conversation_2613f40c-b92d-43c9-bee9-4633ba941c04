import type { PageLoad } from "./$types";
import type { User, Rating } from "@commune/api";

import { error } from "@sveltejs/kit";
import { fixResponseJsonDates, handleUnauthorized } from "$lib";

export const load: PageLoad = async ({ fetch, params, url }) => {
  const [
    userResponse,
    karmaPointsResponse,
    currentUserResponse,
  ] = await Promise.all([
    fetch(`/api/user/${params.id}`),
    fetch(`/api/rating/${params.id}/karma?page=1&size=20`),
    fetch("/api/auth/me"),
  ])

  handleUnauthorized(userResponse, url);
  handleUnauthorized(karmaPointsResponse, url);
  handleUnauthorized(currentUserResponse, url);

  if (!userResponse.ok) {
    throw error(500, {
      message: `Failed to fetch user: ${userResponse.statusText}`,
    });
  }

  if (!karmaPointsResponse.ok) {
    throw error(500, {
      message: `Failed to fetch karma points: ${karmaPointsResponse.statusText}`,
    });
  }

  if (!currentUserResponse.ok) {
    throw error(500, {
      message: `Failed to fetch current user: ${currentUserResponse.statusText}`,
    });
  }

  const user: User.User = await userResponse.json().then(fixResponseJsonDates);
  const karmaPoints: Rating.GetKarmaPointsResponse = await karmaPointsResponse.json();
  const currentUser = await currentUserResponse.json();

  return {
    user,
    karmaPoints,
    currentUser,
    isHasMoreKarma: karmaPoints.length === 20, // If we got a full page, there might be more
  };
};