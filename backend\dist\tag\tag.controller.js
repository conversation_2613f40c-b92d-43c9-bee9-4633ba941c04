"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagController = void 0;
const common_1 = require("@nestjs/common");
const zod_1 = require("../zod");
const current_user_decorator_1 = require("../auth/http/current-user.decorator");
const session_auth_guard_1 = require("../auth/http/session-auth.guard");
const tag_service_1 = require("./tag.service");
const api_1 = require("@commune/api");
let TagController = class TagController {
    constructor(tagService) {
        this.tagService = tagService;
    }
    async getTags(query, input, user) {
        return api_1.Common.parseInput(api_1.Tag.GetTagsOutputSchema, await this.tagService.getTags(input, user));
    }
    async createTag(input, user) {
        return api_1.Common.parseInput(api_1.Tag.CreateTagOutputSchema, await this.tagService.createTag(input, user));
    }
    async updateTag(id, input, user) {
        await this.tagService.updateTag(id, input, user);
    }
    async deleteTag(id, user) {
        await this.tagService.deleteTag(id, user);
    }
};
exports.TagController = TagController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Tag.GetTagsInputSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], TagController.prototype, "getTags", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Tag.CreateTagInputSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TagController.prototype, "createTag", null);
__decorate([
    (0, common_1.Put)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Tag.UpdateTagInputSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], TagController.prototype, "updateTag", null);
__decorate([
    (0, common_1.Delete)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TagController.prototype, "deleteTag", null);
exports.TagController = TagController = __decorate([
    (0, common_1.Controller)("tag"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __metadata("design:paramtypes", [tag_service_1.TagService])
], TagController);
//# sourceMappingURL=tag.controller.js.map