{"version": 3, "file": "localization.service.js", "sourceRoot": "", "sources": ["../../src/localization/localization.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6DAA0D;AAE1D,oCAA+C;AAkB/C,MAAM,aAAa,GAA8B,IAAI,CAAC;AAG/C,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC5B,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAE5C,oBAAoB,CAC1B,aAAkE,EAClE,OAAoC;QAEpC,MAAM,qBAAqB,GAAG,IAAI,GAAG,CACjC,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC;YAChC,YAAY,CAAC,MAAM;YACnB,YAAY,CAAC,KAAK;SACrB,CAAC,CACL,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,IAAI,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpC,OAAO,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC;YAC9C,CAAC;QACL,CAAC;QAED,IAAI,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;YAC3C,OAAO,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAE,CAAC;QACrD,CAAC;QAED,OAAO,aAAa,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACnB,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;SACjC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,OAAO,CACT,KAAoC,EACpC,UAA2C;QAE3C,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YAC3C,GAAG,IAAA,0BAAkB,EAAC,UAAU,CAAC;YACjC,KAAK,EAAE;gBACH,GAAG,KAAK;gBACR,SAAS,EAAE,IAAI;aAClB;SACJ,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,IAAoC;QAChD,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAA0C;QACvD,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,EAAU,EAAE,IAAoC;QAC5D,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACzC,KAAK,EAAE;gBACH,EAAE;gBACF,SAAS,EAAE,IAAI;aAClB;YACD,IAAI;SACP,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,UAAU,CACZ,KAAoC,EACpC,IAAoC;QAEpC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IACtE,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU;QAC1B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,KAAoC;QACrD,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;IACnE,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,EAAU;QACtB,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,KAAoC;QACjD,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;IAChE,CAAC;CACJ,CAAA;AAtFY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAE4B,8BAAa;GADzC,mBAAmB,CAsF/B"}