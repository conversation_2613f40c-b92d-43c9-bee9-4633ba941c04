import { createToken, <PERSON><PERSON> } from "chevrotain";
const Identifier = createToken({
    name: "Identifier",
    pattern: new RegExp([
        "hub",
        "community",
        "author",
        "rating",
        "usefulness",
        "tag",
        "difficulty",
        "title",
        "body",
        "duration",
        "age",
    ].join("|")),
});
const Whitespace = createToken({
    name: "Whitespace",
    pattern: /\s+/,
    group: Lexer.SKIPPED,
});
const And = createToken({
    name: "And",
    pattern: "&&",
});
const Or = createToken({
    name: "Or",
    pattern: "||",
});
const Equals = createToken({
    name: "Equals",
    pattern: "=",
});
const NotEquals = createToken({
    name: "NotEquals",
    pattern: "!=",
});
const GreaterThan = createToken({
    name: "GreaterThan",
    pattern: ">",
});
const GreaterThanOrEqual = createToken({
    name: "GreaterThanOrEqual",
    pattern: ">=",
});
const LessThan = createToken({
    name: "LessThan",
    pattern: "<",
});
const LessThanOrEqual = createToken({
    name: "LessThanOrEqual",
    pattern: "<=",
});
const Like = createToken({
    name: "Like",
    pattern: "~",
});
const LeftParen = createToken({
    name: "LeftParen",
    pattern: "(",
});
const RightParen = createToken({
    name: "RightParen",
    pattern: ")",
});
const LeftSquareBracket = createToken({
    name: "LeftSquareBracket",
    pattern: "[",
});
const RightSquareBracket = createToken({
    name: "RightSquareBracket",
    pattern: "]",
});
const Comma = createToken({
    name: "Comma",
    pattern: ",",
});
const Integer = createToken({
    name: "Integer",
    pattern: /\d+/,
});
const Duration = createToken({
    name: "Duration",
    pattern: /\d+(y|mo|w|d|h|m)/,
});
const StringLiteral = createToken({
    name: "StringLiteral",
    pattern: (text, startOffset) => {
        if (text[startOffset] !== '"')
            return null;
        for (let i = startOffset + 1; i < text.length; i++) {
            if (text[i] === '"' && text[i + 1] === '"') {
                i++;
                continue;
            }
            if (text[i] === '"') {
                return [text.slice(startOffset, i + 1)];
            }
        }
        return null;
    },
    line_breaks: true,
});
const tokens = [
    Whitespace,
    And,
    Or,
    LeftParen,
    RightParen,
    LeftSquareBracket,
    RightSquareBracket,
    Comma,
    Duration,
    Integer,
    StringLiteral,
    NotEquals,
    Equals,
    GreaterThanOrEqual,
    GreaterThan,
    LessThanOrEqual,
    LessThan,
    Like,
    Identifier,
];
const lexer = new Lexer(tokens);
export function tokenize(input) {
    return lexer.tokenize(input);
}
//# sourceMappingURL=tokenize.mjs.map