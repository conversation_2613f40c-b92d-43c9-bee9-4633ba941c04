import type { Common } from "@commune/api";

import { goto } from "$app/navigation";

export function changeLocale(
  currentLocale: Common.LocalizationLocale | null,
  newLocale: Common.LocalizationLocale | null,
) {
  if (currentLocale === newLocale) {
    return;
  }

  let newPath: string;

  switch (true) {
    case currentLocale !== null && newLocale === null:
      newPath = location.pathname.slice(1 + currentLocale.length) || "/";
      break;

    case currentLocale === null && newLocale !== null:
      newPath = `/${newLocale}` + location.pathname;
      break;

    case currentLocale !== newLocale:
      newPath = `/${newLocale}` + location.pathname.slice(1 + currentLocale!.length) || "/";
      break;

    default:
      throw new Error("Invalid locale change params");
  }

  return goto(newPath, { noScroll: true });
}
