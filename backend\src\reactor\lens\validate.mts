import { z } from "zod";
import { Statement as RawStatement } from "./create-ast.mjs";

const op = {
    equals: z.literal("="),
    notEquals: z.literal("!="),
    greaterThan: z.literal(">"),
    greaterThanOrEqual: z.literal(">="),
    lessThan: z.literal("<"),
    lessThanOrEqual: z.literal("<="),
    like: z.literal("~"),
};

type Hub = z.infer<typeof Hub>;
const Hub = z.object({
    identifier: z.literal("hub"),
    operator: z.union([op.equals, op.notEquals]),
    value: z.array(z.string().nanoid()),
});

type Community = z.infer<typeof Community>;
const Community = z.object({
    identifier: z.literal("community"),
    operator: z.union([op.equals, op.notEquals]),
    value: z.array(z.string().nanoid()),
});

type Author = z.infer<typeof Author>;
const Author = z.object({
    identifier: z.literal("author"),
    operator: z.union([op.equals, op.notEquals]),
    value: z.array(z.string().nanoid()),
});

type Rating = z.infer<typeof Rating>;
const Rating = z.object({
    identifier: z.literal("rating"),
    operator: z.union([
        op.equals,
        op.notEquals,
        op.greaterThan,
        op.greaterThanOrEqual,
        op.lessThan,
        op.lessThanOrEqual,
    ]),
    value: z.coerce.number().int(),
});

type Usefulness = z.infer<typeof Usefulness>;
const Usefulness = z.object({
    identifier: z.literal("usefulness"),
    operator: z.union([
        op.equals,
        op.notEquals,
        op.greaterThan,
        op.greaterThanOrEqual,
        op.lessThan,
        op.lessThanOrEqual,
    ]),
    value: z.coerce.number().int().min(0).max(10),
});

type Tag = z.infer<typeof Tag>;
const Tag = z.object({
    identifier: z.literal("tag"),
    operator: z.union([op.equals, op.notEquals]),
    value: z.array(z.string().nanoid()),
});

type Difficulty = z.infer<typeof Difficulty>;
const Difficulty = z.object({
    identifier: z.literal("difficulty"),
    operator: z.union([op.equals, op.notEquals]),
    value: z.array(
        z.union([z.literal("easy"), z.literal("medium"), z.literal("hard")]),
    ),
});

type Title = z.infer<typeof Title>;
const Title = z.object({
    identifier: z.literal("title"),
    operator: op.like,
    value: z
        .string()
        .nonempty()
        .max(2 ** 10),
});

type Body = z.infer<typeof Body>;
const Body = z.object({
    identifier: z.literal("body"),
    operator: op.like,
    value: z
        .string()
        .nonempty()
        .max(2 ** 10),
});

type Duration = z.infer<typeof Duration>;
const Duration = z.object({
    identifier: z.literal("duration"),
    operator: z.union([
        op.greaterThan,
        op.greaterThanOrEqual,
        op.lessThan,
        op.lessThanOrEqual,
    ]),
    value: z.coerce.number().int().min(0),
});

const MINUTE = 60 * 1000;
const HOUR = 60 * MINUTE;
const DAY = 24 * HOUR;
const MONTH = 30 * DAY;
const YEAR = 365 * DAY;

type Age = z.infer<typeof Age>;
const Age = z.object({
    identifier: z.literal("age"),
    operator: z.union([
        op.greaterThan,
        op.greaterThanOrEqual,
        op.lessThan,
        op.lessThanOrEqual,
    ]),
    value: z
        .string()
        .regex(/^\d+(mo|[ywdhm])$/)
        .transform((v) => {
            const match = v.match(/^(?<amount>\d+)(?<unit>mo|[ywdhm])$/);

            if (!match) {
                throw new Error("Invalid duration format");
            }

            const groups = match.groups as {
                amount: `${number}`;
                unit: "y" | "mo" | "w" | "d" | "h" | "m";
            };

            const amount = parseInt(groups.amount);

            switch (groups.unit) {
                case "y":
                    return amount * YEAR;
                case "mo":
                    return amount * MONTH;
                case "w":
                    return amount * 7 * DAY;
                case "d":
                    return amount * DAY;
                case "h":
                    return amount * HOUR;
                case "m":
                    return amount * MINUTE;
            }
        }),
});

type Comparison = Normalize<z.infer<typeof ComparisonSchema>>;
const ComparisonSchema = z.intersection(
    z.object({
        type: z.literal("comparison"),
    }),

    z.union([
        Hub,
        Community,
        Author,
        Rating,
        Usefulness,
        Tag,
        Difficulty,
        Title,
        Body,
        Duration,
        Age,
    ]),
);

type And = {
    type: "and";
    statements: Statement[];
};

type Or = {
    type: "or";
    statements: Statement[];
};

export type Statement = And | Or | Comparison;

export function validate(statement: RawStatement): Statement {
    switch (statement.type) {
        case "and":
        case "or":
            return {
                ...statement,
                statements: statement.statements.map(validate),
            };

        case "comparison": {
            const result = ComparisonSchema.safeParse(statement);

            if (!result.success) {
                throw result.error;
            }

            return result.data;
        }
    }
}
