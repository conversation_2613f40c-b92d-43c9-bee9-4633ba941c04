// @ts-nocheck
import type { PageLoad } from "./$types";
import type { User, Rating } from "@commune/api";

import { error } from "@sveltejs/kit";
import { fixResponseJsonDates, handleUnauthorized } from "$lib";

export const load = async ({ fetch, params, url }: Parameters<PageLoad>[0]) => {
  const [
    userResponse,
    userNoteResponse,
    ratingSummaryResponse,
  ] = await Promise.all([
    fetch(`/api/user/${params.id}`),
    fetch(`/api/user/${params.id}/note`),
    fetch(`/api/rating/${params.id}/summary`),
  ])

  handleUnauthorized(userResponse, url);
  handleUnauthorized(userNoteResponse, url);
  handleUnauthorized(ratingSummaryResponse, url);

  if (!userResponse.ok) {
    throw error(500, {
      message: `Failed to fetch user: ${userResponse.statusText}`,
    });
  }

  if (!userNoteResponse.ok) {
    throw error(500, {
      message: `Failed to fetch user note: ${userNoteResponse.statusText}`,
    });
  }

  if (!ratingSummaryResponse.ok) {
    throw error(500, {
      message: `Failed to fetch rating summary: ${ratingSummaryResponse.statusText}`,
    });
  }

  const user: User.User = await userResponse.json().then(fixResponseJsonDates);
  const userNote: User.GetUserNoteResponse = await userNoteResponse.json();
  const ratingSummary: Rating.GetUserSummaryResponse = await ratingSummaryResponse.json();

  return {
    user,
    userNote: userNote.text,
    ratingSummary,
  };
};
