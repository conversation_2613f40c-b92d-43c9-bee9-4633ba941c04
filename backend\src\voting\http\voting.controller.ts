import {
    Controller,
    Get,
    Post,
    Body,
    Param,
    Delete,
    Query,
} from "@nestjs/common";
import { VotingService } from "../voting.service";
import * as Dto from "./dto";
import { ZodPipe } from "src/zod";
import { Common } from "@commune/api";

@Controller("voting")
export class VotingController {
    constructor(private readonly votingService: VotingService) {}

    @Get()
    async getVotings(
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
    ): Promise<Dto.Voting[]> {
        const votings = await this.votingService.getMany(
            {
                deletedAt: null,
            },
            pagination,
        );

        return Common.parseInput(Dto.Votings, votings);
    }

    @Post()
    async createVoting(
        @Body(new ZodPipe(Dto.CreateVoting)) body: Dto.CreateVoting,
    ): Promise<Dto.Voting> {
        const voting = await this.votingService.create(body);

        return Common.parseInput(Dto.Voting, voting);
    }

    @Get(":id")
    async getVoting(
        @Param("id", new ZodPipe(Common.id)) id: string,
    ): Promise<Dto.Voting> {
        const voting = await this.votingService.getOneOrThrow(id);

        return Common.parseInput(Dto.Voting, voting);
    }

    @Delete(":id")
    async deleteVoting(
        @Param("id", new ZodPipe(Common.id)) id: string,
    ): Promise<void> {
        await this.votingService.deleteOne(id);
    }
}
