// image
model Image {
    @@map("images")

    id String @id @default(nanoid())

    users        User[]        @relation("user_images")
    communes     Commune[]     @relation("commune_images")
    votings      Voting[]      @relation("voting_images")
    merchandises Merchandise[] @relation("merchandise_images")

    reactorHubs        ReactorHub[]       @relation("reactor_hub_image")
    reactorCommunities ReactorCommunity[] @relation("reactor_community_image")

    url    String

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}
