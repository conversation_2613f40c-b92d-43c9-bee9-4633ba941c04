/*
  Warnings:

  - You are about to drop the column `isHead` on the `commune_members` table. All the data in the column will be lost.

*/
-- <PERSON><PERSON><PERSON>num
CREATE TYPE "commune_invitation_status" AS ENUM ('pending', 'accepted', 'rejected', 'expired');

-- CreateEnum
CREATE TYPE "commune_join_request_status" AS ENUM ('pending', 'accepted', 'rejected');

-- AlterTable
ALTER TABLE "commune_members" DROP COLUMN "isHead",
ADD COLUMN     "is_head" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "commune_invitations" (
    "id" TEXT NOT NULL,
    "commune_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "expires_at" TIMESTAMPTZ(3) NOT NULL,
    "status" "commune_invitation_status" NOT NULL DEFAULT 'pending',
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "commune_invitations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "commune_join_requests" (
    "id" TEXT NOT NULL,
    "commune_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "status" "commune_join_request_status" NOT NULL DEFAULT 'pending',
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "commune_join_requests_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "commune_invitations" ADD CONSTRAINT "commune_invitations_commune_id_fkey" FOREIGN KEY ("commune_id") REFERENCES "communes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "commune_invitations" ADD CONSTRAINT "commune_invitations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "commune_join_requests" ADD CONSTRAINT "commune_join_requests_commune_id_fkey" FOREIGN KEY ("commune_id") REFERENCES "communes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "commune_join_requests" ADD CONSTRAINT "commune_join_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
