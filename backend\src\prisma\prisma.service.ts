import { Injectable, OnM<PERSON>ule<PERSON><PERSON>roy, OnModuleInit } from "@nestjs/common";
import { Prisma, PrismaClient } from "@prisma/client";

@Injectable()
export class PrismaService
    extends PrismaClient<Prisma.PrismaClientOptions, "query">
    implements OnModuleInit, OnModuleDestroy
{
    constructor() {
        super({
            log: [
                {
                    level: "query",
                    emit: "event",
                },
            ],
        });
    }

    onModuleInit() {
        return this.$connect().then(() => {
            this.$on("query", (e) => {
                console.log("Query:", e.query);
                console.log("Params:", e.params);
                console.log("Duration:", e.duration, "ms");
            });
        });
    }

    onModuleDestroy() {
        return this.$disconnect();
    }
}
