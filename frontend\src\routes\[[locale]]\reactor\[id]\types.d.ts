import type { Common } from "@commune/api";

export type RatingStatus = "like" | "dislike";

export type PostEntity = {
  id: string;

  path: string;

  title: Common.Localizations;
  body: Common.Localizations;

  author: {
    id: string;
    name: Common.Localizations;
    avatar: string | null;
  };

  rating: {
    likes: number;
    dislikes: number;
    status: RatingStatus | null;
  };

  usefulness: {
    value: number | null;
    count: number;
    totalValue: number | null;
  };

  tags: string[];

  createdAt: Date;
  updatedAt: Date;
};

export type CommentEntity = {
  id: string;

  path: string;

  isAnonymous: boolean;
  anonimityReason: string | null;
  author: {
    id: string;
    name: Common.Localizations;
    avatar: string | null;
  } | null;

  rating: {
    likes: number;
    dislikes: number;
    status: RatingStatus | null;
  };

  body: Common.Localizations | null;

  childrenCount: number;

  deleteReason: string | null;

  /**
   * Used when the comment is newly created and must be placed at the top of siblings.
   * Server doesn't return this field, it's client-side only.
   */
  isMustBeTop?: boolean;

  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
};
