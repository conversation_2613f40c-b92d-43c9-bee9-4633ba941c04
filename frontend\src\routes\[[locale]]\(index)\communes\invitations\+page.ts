import type { Common, Commune } from "@commune/api";
import type { PageLoad } from "./$types";

import { handleUnauthorized } from "$lib";

export interface CommuneInvitation {
  id: string;
  communeId: string;
  userId: string;
  status: Commune.CommuneInvitationStatus;
  createdAt: string;
  updatedAt: string;
}

export interface CommuneInvitationWithDetails extends CommuneInvitation {
  commune: {
    id: string;
    name: Common.Localizations;
    description: Common.Localizations;
    memberCount: number;
    headMember: {
      actorType: "user" | "commune";
      actorId: string;
      name: Common.Localizations;
    };
    images: Common.Images;
    createdAt: string;
    updatedAt: string;
  };
}

export const load: PageLoad = async ({ fetch, url }) => {
  const response = await fetch("/api/commune/invitation?page=1&size=20");

  handleUnauthorized(response, url);

  const invitations: CommuneInvitation[] = response.ok ? await response.json() : [];

  // Fetch commune details for each invitation
  const invitationsWithDetails: (CommuneInvitationWithDetails | null)[] = await Promise.all(
    invitations.map(async (invitation) => {
      try {
        const communeResponse = await fetch(`/api/commune/${invitation.communeId}`);
        if (communeResponse.ok) {
          const commune = await communeResponse.json();
          return {
            ...invitation,
            commune,
          } as CommuneInvitationWithDetails;
        }
        // If commune fetch fails, we'll handle this in the component
        console.warn(`Failed to fetch commune ${invitation.communeId}: ${communeResponse.statusText}`);
        return null;
      } catch (error) {
        console.error(`Failed to fetch commune ${invitation.communeId}:`, error);
        return null;
      }
    })
  );

  // Filter out failed fetches
  const validInvitations = invitationsWithDetails.filter(
    (invitation): invitation is CommuneInvitationWithDetails => invitation !== null
  );

  return {
    invitations: validInvitations,
    isHasMoreInvitations: invitations.length === 20, // If we got a full page, there might be more
  };
};
