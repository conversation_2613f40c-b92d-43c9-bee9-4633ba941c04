import type { Common, Auth, Commune } from "@commune/api";
import type { PageLoad } from "./$types";

import { handleUnauthorized } from "$lib";

export interface CommuneInvitation {
  id: string;
  communeId: string;
  userId: string;
  status: "pending" | "accepted" | "rejected" | "expired";
  createdAt: string;
  updatedAt: string;
}

export interface CommuneInvitationWithUserDetails extends CommuneInvitation {
  user: {
    id: string;
    email: string;
    name: Common.Localizations;
    description: Common.Localizations;
    images?: {
      id: string;
      url: string;
      source: string;
    }[];
    createdAt: string;
    updatedAt: string;
  };
}

export const load: PageLoad = async ({ fetch, params, url, parent }) => {
  const [
    user,
    communeResponse
  ] = await Promise.all([
    fetch("/api/auth/me").then<Auth.GetMeResponse>(r => r.json()),
    fetch(`/api/commune/${params.id}`),
  ]);

  handleUnauthorized(communeResponse, url);
  
  if (!communeResponse.ok) {
    throw new Error(`Failed to fetch commune: ${communeResponse.statusText}`);
  }
  
  const commune: Commune.Commune = await communeResponse.json();
  
  // Check if user has permission to view invitations (admin or head member)
  const isAdmin = user?.role === "admin";
  const isHeadMember = user && commune.headMember.actorType === "user" && commune.headMember.actorId === user.id;
  
  if (!isAdmin && !isHeadMember) {
    throw new Error("Access denied: You must be an admin or commune head to view invitations");
  }

  // Fetch invitations for this commune
  const invitationsResponse = await fetch(`/api/commune/${params.id}/invitation?page=1&size=20`);
  handleUnauthorized(invitationsResponse, url);

  const invitations: CommuneInvitation[] = invitationsResponse.ok ? await invitationsResponse.json() : [];

  // Fetch user details for each invitation
  const invitationsWithUserDetails: (CommuneInvitationWithUserDetails | null)[] = await Promise.all(
    invitations.map(async (invitation) => {
      try {
        const userResponse = await fetch(`/api/user/${invitation.userId}`);
        if (userResponse.ok) {
          const userData = await userResponse.json();
          return {
            ...invitation,
            user: userData,
          } as CommuneInvitationWithUserDetails;
        }
        // If user fetch fails, we'll handle this in the component
        console.warn(`Failed to fetch user ${invitation.userId}: ${userResponse.statusText}`);
        return null;
      } catch (error) {
        console.error(`Failed to fetch user ${invitation.userId}:`, error);
        return null;
      }
    })
  );

  // Filter out failed fetches
  const validInvitations = invitationsWithUserDetails.filter(
    (invitation): invitation is CommuneInvitationWithUserDetails => invitation !== null
  );

  return {
    commune,
    invitations: validInvitations,
    isHasMoreInvitations: invitations.length === 20, // If we got a full page, there might be more
    userPermissions: {
      isAdmin,
      isHeadMember,
      canManageInvitations: isAdmin || isHeadMember,
    },
  };
};
