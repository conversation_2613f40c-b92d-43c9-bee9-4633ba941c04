import { z } from "zod";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { createTransport, Transporter } from "nodemailer";
import SMTPTransport from "nodemailer/lib/smtp-transport";

type SendEmailDto = {
    from: string;
    to: string[];
    subject: string;
    text: string;
};

@Injectable()
export class EmailService {
    private readonly transporter: Transporter;

    private readonly isIgnoreEmailErrors: boolean;
    private readonly isDisableOtpEmail: boolean;

    constructor(private readonly configService: ConfigService) {
        this.isIgnoreEmailErrors = Boolean(
            this.configService.get("IGNORE_EMAIL_ERRORS"),
        );

        this.isDisableOtpEmail = Boolean(
            this.configService.get("DISABLE_OTP_EMAIL"),
        );

        const host = z
            .string()
            .nonempty()
            .parse(this.configService.get("EMAIL_HOST"));
        const port = z.coerce
            .number()
            .int()
            .positive()
            .parse(this.configService.get("EMAIL_PORT"));
        const user = z
            .string()
            .nonempty()
            .parse(this.configService.get("EMAIL_USER"));
        const pass = z
            .string()
            .nonempty()
            .parse(this.configService.get("EMAIL_PASSWORD"));

        const rawRejectUnauthorized = this.configService.get<string>(
            "EMAIL_REJECT_UNAUTHORIZED",
        );
        const rejectUnauthorized =
            rawRejectUnauthorized === undefined
                ? undefined
                : Boolean(rawRejectUnauthorized);

        const options = {
            host,
            secure: false,
            port,

            auth: {
                user,
                pass,
            },

            tls: {
                rejectUnauthorized,
            },
        } satisfies SMTPTransport.Options;

        // console.log({ options });

        this.transporter = createTransport(options);
    }

    joinAddress(sender: string, domain: string) {
        return `${sender}@${domain}`;
    }

    async send(dto: SendEmailDto) {
        try {
            console.dir({ dto }, { depth: null });

            if (this.isDisableOtpEmail) {
                console.debug("EmailService.send.otpEmailDisabled");

                return false;
            }

            await this.transporter.verify();

            await this.transporter.sendMail({
                from: dto.from,
                to: dto.to,
                subject: dto.subject,
                text: dto.text,
            });

            return true;
        } catch (e) {
            if (!this.isIgnoreEmailErrors) {
                throw e;
            }

            return false;
        }
    }
}
