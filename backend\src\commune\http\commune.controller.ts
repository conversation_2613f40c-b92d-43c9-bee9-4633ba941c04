import {
    Controller,
    Get,
    Post,
    Body,
    Param,
    UseGuards,
    Query,
    NotFoundException,
    Put,
    Delete,
    InternalServerErrorException,
    UseInterceptors,
    UploadedFiles,
    BadRequestException,
    ParseFilePipe,
    MaxFileSizeValidator,
    FileTypeValidator,
} from "@nestjs/common";
import { FilesInterceptor } from "@nestjs/platform-express";
import { z, ZodPipe } from "src/zod";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { HttpSessionAuthGuard } from "src/auth/http/session-auth.guard";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { CommuneService } from "../commune.service";
import {
    ALLOWED_FILE_TYPES,
    MAX_FILE_SIZE,
    MAX_FILES_COUNT,
} from "./file-upload.dto";
import { CommuneMemberService } from "../commune-member.service";
import { UserService } from "src/user/user.service";

import { Common, Commune } from "@commune/api";

@Controller("commune")
@UseGuards(HttpSessionAuthGuard)
export class CommuneController {
    constructor(
        private readonly communeService: CommuneService,
        private readonly communeMemberService: CommuneMemberService,
        private readonly userService: UserService,
    ) {}

    @Get("invitation")
    async getInvitations(
        @HttpCurrentUser() user: CurrentUser,
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
    ) {
        const invitations = await this.communeService.getInvitationsForUser(
            user.id,
            pagination,
        );

        return Common.parseInput(Commune.CommuneInvitationsSchema, invitations);
    }

    @Get(":id/invitation")
    async getInvitationsForCommune(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
    ) {
        const invitations = await this.communeService.getInvitationsForCommune(
            id,
            pagination,
        );

        return Common.parseInput(Commune.CommuneInvitationsSchema, invitations);
    }

    @Put("invitation")
    async createInvitation(
        @Body(new ZodPipe(Commune.CreateCommuneInvitationRequestSchema))
        body: Commune.CreateCommuneInvitationRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const result = await this.communeService.createInvitation(
            body.communeId,
            body.userId,
            user,
        );

        return Common.parseInput(Common.ObjectWithIdSchema, result);
    }

    @Delete("invitation/:invitationId")
    async deleteInvitation(
        @Param("invitationId", new ZodPipe(Common.id)) invitationId: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.communeService.deleteInvitation(invitationId, user);
    }

    @Post("invitation/:invitationId/accept")
    async acceptInvitation(
        @Param("invitationId", new ZodPipe(Common.id)) invitationId: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.communeService.acceptInvitation(invitationId, user);
    }

    @Post("invitation/:invitationId/reject")
    async rejectInvitation(
        @Param("invitationId", new ZodPipe(Common.id)) invitationId: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.communeService.rejectInvitation(invitationId, user);
    }

    @Get("join-request")
    async getJoinRequests(
        @HttpCurrentUser() user: CurrentUser,
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
    ) {
        const joinRequests = await this.communeService.getJoinRequestsForUser(
            user.id,
            pagination,
        );

        return Common.parseInput(
            Commune.CommuneJoinRequestsSchema,
            joinRequests,
        );
    }

    @Get(":id/join-request")
    async getJoinRequestsForCommune(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
    ) {
        const joinRequests =
            await this.communeService.getJoinRequestsForCommune(id, pagination);

        return Common.parseInput(
            Commune.CommuneJoinRequestsSchema,
            joinRequests,
        );
    }

    @Put("join-request")
    async createJoinRequest(
        @Body(new ZodPipe(Commune.CreateCommuneJoinRequestRequestSchema))
        body: Commune.CreateCommuneJoinRequestRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const result = await this.communeService.createJoinRequest(
            body.communeId,
            body.userId,
            user,
        );

        return Common.parseInput(Common.ObjectWithIdSchema, result);
    }

    @Delete("join-request/:joinRequestId")
    async deleteJoinRequest(
        @Param("joinRequestId", new ZodPipe(Common.id)) joinRequestId: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.communeService.deleteJoinRequest(joinRequestId, user);
    }

    @Post("join-request/:joinRequestId/accept")
    async acceptJoinRequest(
        @Param("joinRequestId", new ZodPipe(Common.id)) joinRequestId: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.communeService.acceptJoinRequest(joinRequestId, user);
    }

    @Post("join-request/:joinRequestId/reject")
    async rejectJoinRequest(
        @Param("joinRequestId", new ZodPipe(Common.id)) joinRequestId: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.communeService.rejectJoinRequest(joinRequestId, user);
    }

    private async getCommuneToReturn(
        commune: NonNullable<Awaited<ReturnType<CommuneService["getOne"]>>>,
    ) {
        const headMember = commune.members.find((member) => member.isHead);

        if (!headMember) {
            throw new InternalServerErrorException(
                getError("commune_head_member_not_found"),
            );
        }

        const headMemberEntity =
            headMember.actorType === "user"
                ? await this.userService.getOne(headMember.actorId)
                : await this.communeService.getOne(headMember.actorId);

        if (!headMemberEntity) {
            throw new InternalServerErrorException(
                getError("commune_head_member_entity_not_found"),
            );
        }

        const memberCount = commune.members.length;

        return {
            ...commune,
            headMember: {
                actorType: headMember.actorType,
                actorId: headMember.actorId,
                name: headMemberEntity.name,
            },
            memberCount,
            // Ensure images are included
            images: commune.images || [],
        };
    }

    @Get()
    async getCommunes(
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
    ) {
        const communes = await this.communeService.getMany(
            {
                deletedAt: null,
            },
            pagination,
        );

        const communesToReturn = await Promise.all(
            communes.map((commune) => this.getCommuneToReturn(commune)),
        );

        return Common.parseInput(Commune.CommunesSchema, communesToReturn);
    }

    @Post()
    @UseInterceptors(FilesInterceptor("images", MAX_FILES_COUNT))
    async createCommune(
        @Body("data", new ZodPipe(Commune.CreateCommuneRequestSchema))
        body: Commune.CreateCommuneRequest,
        @HttpCurrentUser() user: CurrentUser,

        @UploadedFiles(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
                    new FileTypeValidator({
                        fileType: ALLOWED_FILE_TYPES.join("|"),
                    }),
                ],
                fileIsRequired: false,
            }),
        )
        files?: Array<Express.Multer.File>,
    ) {
        try {
            // Create the commune
            const commune = await this.communeService.create(
                {
                    headUserId: body.headUserId,
                    name: body.name,
                    description: body.description,
                },
                user,
            );

            // Upload images if provided
            if (files && files.length > 0) {
                try {
                    await this.communeService.uploadCommuneImages(
                        commune.id,
                        files,
                    );
                } catch (error) {
                    console.error("Failed to upload images:", error);
                    // Continue even if image upload fails
                }
            }

            return Common.parseInput(
                Commune.CommuneSchema,
                await this.getCommuneToReturn(commune),
            );
        } catch (error) {
            console.error("Error processing form data:", error);

            if (error instanceof z.ZodError) {
                throw new BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }

            throw new BadRequestException("Failed to process form data");
        }
    }

    @Post(":id/images")
    @UseInterceptors(FilesInterceptor("images", MAX_FILES_COUNT))
    async uploadCommuneImages(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
        @UploadedFiles(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
                    new FileTypeValidator({
                        fileType: ALLOWED_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        files: Array<Express.Multer.File>,
    ) {
        try {
            // Check permissions
            await this.communeService.canChange(id, user);

            if (!files || files.length === 0) {
                throw new BadRequestException("No files uploaded");
            }

            const images = await this.communeService.uploadCommuneImages(
                id,
                files,
            );

            return Common.parseInput(Common.ImagesSchema, images);
        } catch (error) {
            console.error("Error uploading images:", error);

            if (error instanceof BadRequestException) {
                throw error;
            }

            throw new BadRequestException("Failed to upload images");
        }
    }

    @Get(":id")
    async getCommune(@Param("id", new ZodPipe(Common.id)) id: string) {
        const commune = await this.communeService.getOne(id);

        if (!commune) {
            throw new NotFoundException(getError("commune_not_found"));
        }

        return Common.parseInput(
            Commune.CommuneSchema,
            await this.getCommuneToReturn(commune),
        );
    }

    @Put(":id")
    async updateCommune(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Commune.UpdateCommuneRequestSchema))
        body: Commune.UpdateCommuneRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        try {
            // Check if the commune exists
            const commune = await this.communeService.getOne(id);

            if (!commune) {
                throw new NotFoundException(getError("commune_not_found"));
            }

            // Update the commune
            const updatedCommune = await this.communeService.update(
                id,
                {
                    name: {
                        deleteMany: {},
                        create: body.name.map((item) => ({
                            key: "name",
                            locale: item.locale,
                            value: item.value,
                        })),
                    },
                    description: {
                        deleteMany: {},
                        create: body.description.map((item) => ({
                            key: "description",
                            locale: item.locale,
                            value: item.value,
                        })),
                    },
                },
                user,
            );

            return Common.parseInput(
                Commune.CommuneSchema,
                await this.getCommuneToReturn(updatedCommune),
            );
        } catch (error) {
            console.error("Error updating commune:", error);

            if (error instanceof z.ZodError) {
                throw new BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }

            if (
                error instanceof NotFoundException ||
                error instanceof BadRequestException
            ) {
                throw error;
            }

            throw new BadRequestException("Failed to update commune");
        }
    }

    @Delete(":id")
    async deleteCommune(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.communeService.softDeleteOneCascade(id, user);
    }

    @Get(":id/member")
    async getCommuneMembers(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
    ) {
        const communeMembers = await this.communeMemberService.getMany(
            {
                communeId: id,
                deletedAt: null,
            },
            pagination,
        );

        return Common.parseInput(Commune.CommuneMembersSchema, communeMembers);
    }

    @Get(":id/member/:memberId")
    async getCommuneMember(
        @Param("memberId", new ZodPipe(Common.id)) memberId: string,
    ) {
        const communeMember = await this.communeMemberService.getOne(memberId);

        if (!communeMember) {
            throw new NotFoundException(getError("commune_member_not_found"));
        }

        return Common.parseInput(Commune.CommuneMemberSchema, communeMember);
    }

    @Post(":id/member")
    async createCommuneMember(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Commune.CreateCommuneMemberRequestSchema))
        body: Commune.CreateCommuneMemberRequest,
    ) {
        const commune = await this.communeMemberService.createOne({
            commune: {
                connect: {
                    id,
                    deletedAt: null,
                },
            },
            actorType: "user",
            actorId: body.userId,
        });

        return Common.parseInput(Commune.CommuneMemberSchema, commune);
    }

    // @Put(":id/member/:memberId")
    // async updateCommuneMember(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Param("memberId", new ZodPipe(Common.id)) memberId: string,
    //     @Body(new ZodPipe(Commune.UpdateCommuneMemberRequestSchema))
    //     body: Commune.UpdateCommuneMemberRequest,
    // ) {
    //     // Use memberId parameter to ensure we're updating the correct member
    //     const communeMember = await this.communeMemberService.getOne(memberId);

    //     if (!communeMember) {
    //         throw new NotFoundException(getError("commune_member_not_found"));
    //     }

    //     if (communeMember.communeId !== id) {
    //         throw new BadRequestException(
    //             "Member does not belong to the specified commune",
    //         );
    //     }

    //     const updatedMember = await this.communeMemberService.updateOne(
    //         memberId, // Use memberId instead of id
    //         body,
    //     );

    //     return Common.parseInput(Commune.CommuneMemberSchema, updatedMember);
    // }

    @Delete(":id/member/:memberId")
    async deleteCommuneMember(
        @Param("memberId", new ZodPipe(Common.id)) memberId: string,
    ) {
        await this.communeMemberService.softDeleteOne(memberId);

        return true;
    }

    @Post(":id/transfer-head-status")
    async transferHeadStatus(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Commune.TransferHeadStatusRequestSchema))
        body: Commune.TransferHeadStatusRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.communeService.transferHeadStatus(
            id,
            body.newHeadUserId,
            user,
        );
    }
}
