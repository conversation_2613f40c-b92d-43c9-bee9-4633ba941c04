/*
  Warnings:

  - A unique constraint covering the columns `[user_id]` on the table `user_karma_spendable_points` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[source_user_id,target_user_id,entity_type,entity_id]` on the table `user_ratings` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `entity_id` to the `user_ratings` table without a default value. This is not possible if the table is not empty.
  - Added the required column `entity_type` to the `user_ratings` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "UserRatingEntityType" AS ENUM ('post', 'comment');

-- AlterTable
ALTER TABLE "user_ratings" ADD COLUMN     "entity_id" TEXT NOT NULL,
ADD COLUMN     "entity_type" "UserRatingEntityType" NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "user_karma_spendable_points_user_id_key" ON "user_karma_spendable_points"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_ratings_source_user_id_target_user_id_entity_type_enti_key" ON "user_ratings"("source_user_id", "target_user_id", "entity_type", "entity_id");

-- AddForeignKey
ALTER TABLE "user_ratings" ADD CONSTRAINT "user_ratings_source_user_id_fkey" FOREIGN KEY ("source_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_ratings" ADD CONSTRAINT "user_ratings_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_karma_given_points" ADD CONSTRAINT "user_karma_given_points_source_user_id_fkey" FOREIGN KEY ("source_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_karma_given_points" ADD CONSTRAINT "user_karma_given_points_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_feedbacks" ADD CONSTRAINT "user_feedbacks_source_user_id_fkey" FOREIGN KEY ("source_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_feedbacks" ADD CONSTRAINT "user_feedbacks_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
