<script lang="ts">
  import type { CommuneJoinRequestWithDetails } from "./+page";
  import { onMount } from "svelte";

  import { fetchWithAuth } from "$lib";
  import CommuneImageCarousel from "../commune-image-carousel.svelte";

  const i18n = {
    en: {
      _page: {
        title: "Join Requests — Commune",
      },
      joinRequests: "Join Requests",
      loading: "Loading...",
      noJoinRequests: "No join requests found",
      noDescription: "No description",
      member: "member",
      members: "members",
      headMember: "Head",
      errorFetchingJoinRequests: "Failed to fetch join requests",
      errorOccurred: "An error occurred while fetching join requests",
      loadingMore: "Loading more join requests...",
      cancel: "Cancel Request",
      pending: "Pending",
      accepted: "Accepted",
      rejected: "Rejected",
      requestedOn: "Requested on",
      cancelingRequest: "Canceling...",
      errorCancelingRequest: "Failed to cancel join request",
      requestCanceled: "Join request canceled",
      backToCommunes: "Back to Communes",
      viewCommune: "View Commune",
      awaitingApproval: "Awaiting approval from commune head",
    },
    ru: {
      _page: {
        title: "Заявки на вступление — Коммуна",
      },
      joinRequests: "Заявки на вступление",
      loading: "Загрузка...",
      noJoinRequests: "Заявки не найдены",
      noDescription: "Нет описания",
      member: "участник",
      members: "участников",
      headMember: "Глава",
      errorFetchingJoinRequests: "Не удалось загрузить заявки",
      errorOccurred: "Произошла ошибка при загрузке заявок",
      loadingMore: "Загружаем больше заявок...",
      cancel: "Отменить заявку",
      pending: "Ожидает",
      accepted: "Принято",
      rejected: "Отклонено",
      requestedOn: "Подана",
      cancelingRequest: "Отменяем...",
      errorCancelingRequest: "Не удалось отменить заявку",
      requestCanceled: "Заявка отменена",
      backToCommunes: "Назад к коммунам",
      viewCommune: "Посмотреть коммуну",
      awaitingApproval: "Ожидает одобрения главы коммуны",
    },
  };

  const { data } = $props();
  const { locale, routeLocale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes
  let joinRequests = $state(data.joinRequests);
  let error = $state<string | null>(null);

  const PAGE_SIZE = 20;

  let isLoadingMore = $state(false);
  let currentPage = $state(1);
  let isHasMoreJoinRequests = $state(data.isHasMoreJoinRequests);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  // Track loading states for individual join requests
  let loadingStates = $state<Record<string, "canceling" | null>>({});

  // Function to load more join requests
  async function loadMoreJoinRequests() {
    if (isLoadingMore || !isHasMoreJoinRequests) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;
      const response = await fetchWithAuth(
        `/api/commune/join-request?page=${nextPage}&size=${PAGE_SIZE}`,
      );

      if (!response.ok) {
        throw new Error(`${t.errorFetchingJoinRequests}: ${response.statusText}`);
      }

      const newJoinRequests: any[] = await response.json();

      // Fetch commune details for new join requests
      const joinRequestsWithDetails = await Promise.all(
        newJoinRequests.map(async (joinRequest) => {
          try {
            const communeResponse = await fetchWithAuth(`/api/commune/${joinRequest.communeId}`);
            if (communeResponse.ok) {
              const commune = await communeResponse.json();
              return {
                ...joinRequest,
                commune,
              };
            }
            return null;
          } catch (error) {
            console.error(`Failed to fetch commune ${joinRequest.communeId}:`, error);
            return null;
          }
        }),
      );

      const validJoinRequests = joinRequestsWithDetails.filter(
        (joinRequest): joinRequest is CommuneJoinRequestWithDetails => joinRequest !== null,
      );

      // Append new join requests to existing list
      joinRequests = [...joinRequests, ...validJoinRequests];
      currentPage = nextPage;

      // Check if there are more join requests to load
      isHasMoreJoinRequests = newJoinRequests.length === PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  // Function to cancel a join request
  async function cancelJoinRequest(joinRequestId: string) {
    loadingStates[joinRequestId] = "canceling";
    error = null;

    try {
      const response = await fetchWithAuth(`/api/commune/join-request/${joinRequestId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error(`${t.errorCancelingRequest}: ${response.statusText}`);
      }

      // Remove the join request from the list
      joinRequests = joinRequests.filter((req) => req.id !== joinRequestId);
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorCancelingRequest;
      console.error(err);
    } finally {
      loadingStates[joinRequestId] = null;
    }
  }

  // Setup intersection observer for infinite scroll
  onMount(() => {
    let observer: IntersectionObserver;

    const setupObserver = () => {
      if (!sentinelElement) return;

      observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && isHasMoreJoinRequests && !isLoadingMore) {
            loadMoreJoinRequests();
          }
        },
        {
          rootMargin: "100px", // Start loading 100px before the sentinel comes into view
          threshold: 0.1,
        },
      );

      observer.observe(sentinelElement);
    };

    // Try to setup observer immediately, or wait for element to be available
    if (sentinelElement) {
      setupObserver();
    } else {
      // Use a small delay to allow the DOM to render
      setTimeout(setupObserver, 100);
    }

    // Cleanup observer on component destroy
    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  });

  // Helper function to get status badge class
  function getStatusBadgeClass(status: string) {
    switch (status) {
      case "pending":
        return "bg-warning text-dark";
      case "accepted":
        return "bg-success";
      case "rejected":
        return "bg-danger";
      default:
        return "bg-secondary";
    }
  }

  // Helper function to get status text
  function getStatusText(status: string) {
    switch (status) {
      case "pending":
        return t.pending;
      case "accepted":
        return t.accepted;
      case "rejected":
        return t.rejected;
      default:
        return status;
    }
  }

  // Helper function to format date
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString(locale, {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container my-4 mb-5">
  <div class="d-flex justify-content-between align-items-center my-4">
    <h1>{t.joinRequests}</h1>
    <a href={toLocaleHref("/communes")} class="btn btn-outline-secondary">
      {t.backToCommunes}
    </a>
  </div>

  {#if joinRequests.length === 0}
    <div class="text-center py-5">
      <p class="text-muted">{t.noJoinRequests}</p>
    </div>
  {:else}
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
      {#each joinRequests as joinRequest (joinRequest.id)}
        <div class="col">
          <div class="card h-100 shadow-sm">
            <!-- Commune image carousel -->
            <CommuneImageCarousel
              images={joinRequest.commune.images}
              communeId={joinRequest.communeId}
              {locale}
            />

            <div class="card-body d-flex flex-column">
              <!-- Status badge -->
              <div class="d-flex justify-content-between align-items-start mb-2">
                <span class={`badge ${getStatusBadgeClass(joinRequest.status)}`}>
                  {getStatusText(joinRequest.status)}
                </span>
                <small class="text-muted">
                  {t.requestedOn}
                  {formatDate(joinRequest.createdAt)}
                </small>
              </div>

              <!-- Commune name -->
              <h5 class="card-title fs-5 text-truncate mb-2">
                {getAppropriateLocalization(joinRequest.commune?.name) || "Unknown Commune"}
              </h5>

              <!-- Commune description -->
              <p class="card-text text-muted small mb-3" style="height: 3rem; overflow: hidden">
                {getAppropriateLocalization(joinRequest.commune.description) || t.noDescription}
              </p>

              <!-- Commune info -->
              <div class="mb-3">
                <span class="badge bg-primary mb-2">
                  {joinRequest.commune?.memberCount || 0}
                  {(joinRequest.commune?.memberCount || 0) === 1 ? t.member : t.members}
                </span>

                <div class="small text-muted">
                  <div>{t.headMember}:</div>
                  <div class="d-flex flex-column">
                    {getAppropriateLocalization(joinRequest.commune.headMember.name) || "Unknown"}
                  </div>
                </div>
              </div>

              <!-- Action buttons for pending join requests -->
              {#if joinRequest.status === "pending"}
                <div class="mt-auto">
                  <div class="alert alert-info small mb-2">
                    {t.awaitingApproval}
                  </div>
                  <button
                    class="btn btn-outline-danger w-100"
                    disabled={loadingStates[joinRequest.id] === "canceling"}
                    onclick={() => cancelJoinRequest(joinRequest.id)}
                  >
                    {#if loadingStates[joinRequest.id] === "canceling"}
                      <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                      {t.cancelingRequest}
                    {:else}
                      {t.cancel}
                    {/if}
                  </button>
                </div>
              {:else}
                <!-- For non-pending join requests, show link to commune -->
                <div class="mt-auto">
                  <a
                    href={toLocaleHref(
                      `/communes/${joinRequest.commune?.id || joinRequest.communeId}`,
                    )}
                    class="btn btn-outline-primary w-100"
                  >
                    {t.viewCommune}
                  </a>
                </div>
              {/if}
            </div>
          </div>
        </div>
      {/each}
    </div>
  {/if}

  <!-- Infinite scroll sentinel element -->
  {#if isHasMoreJoinRequests}
    <div bind:this={sentinelElement} class="text-center py-3">
      {#if isLoadingMore}
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">{t.loadingMore}</span>
        </div>
        <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
      {/if}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {/if}
</div>

<style>
  .hover-card {
    cursor: pointer;
    transition: transform 0.2s;
  }

  .hover-card:hover {
    transform: translateY(-5px);
  }
</style>
