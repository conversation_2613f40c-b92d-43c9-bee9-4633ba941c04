"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneMemberService = void 0;
const common_1 = require("@nestjs/common");
const base_service_1 = require("../common/base-service");
const errors_1 = require("../common/errors");
const prisma_service_1 = require("../prisma/prisma.service");
const utils_1 = require("../utils");
let CommuneMemberService = class CommuneMemberService extends base_service_1.BaseService {
    constructor(prisma) {
        super("commune-member");
        this.prisma = prisma;
    }
    async canGet(id, user) {
        await this.getOneOrThrow(id);
        return true;
    }
    async check(ids) {
        return await this._check(ids, this.prisma.communeMember, "commune-member");
    }
    async getFullMember(member) {
        if (member.actorType === "user") {
            await this.prisma.user.findUniqueOrThrow({
                where: { id: member.actorId },
            });
            const user = await this.prisma.user.findUniqueOrThrow({
                where: { id: member.actorId },
                include: {
                    name: true,
                    images: true,
                },
            });
            return {
                ...member,
                name: user.name,
                images: user.images,
            };
        }
        if (member.actorType === "commune") {
            const commune = await this.prisma.commune.findUniqueOrThrow({
                where: { id: member.actorId },
                include: {
                    name: true,
                    images: true,
                },
            });
            return {
                ...member,
                name: commune.name,
                images: commune.images,
            };
        }
        throw new common_1.InternalServerErrorException(...(0, errors_1.getError)("commune_member_actor_type_wrong"));
    }
    async getOne(id) {
        const member = await this.prisma.communeMember.findUnique({
            where: { id, deletedAt: null },
        });
        return member && (await this.getFullMember(member));
    }
    async getOneOrThrow(id) {
        const communeMember = await this.getOne(id);
        if (!communeMember) {
            throw this.createNotFoundException();
        }
        return communeMember;
    }
    async getMany(where, pagination) {
        const members = await this.prisma.communeMember.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
        });
        return await Promise.all(members.map((member) => this.getFullMember(member)));
    }
    async createOne(data) {
        const member = await this.prisma.communeMember.create({ data });
        return await this.getFullMember(member);
    }
    async createMany(data) {
        return await this.prisma.communeMember.createMany({ data });
    }
    async updateOne(id, data) {
        const member = await this.prisma.communeMember.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
        return await this.getFullMember(member);
    }
    async updateMany(where, data) {
        return await this.prisma.communeMember.updateMany({
            where: {
                ...where,
                deletedAt: null,
            },
            data,
        });
    }
    async softDeleteOne(id) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }
    async softDeleteMany(where) {
        return await this.updateMany({ ...where, deletedAt: null }, { deletedAt: new Date() });
    }
    async deleteOne(id) {
        return await this.prisma.communeMember.delete({ where: { id } });
    }
    async deleteMany(where) {
        return await this.prisma.communeMember.deleteMany({ where });
    }
};
exports.CommuneMemberService = CommuneMemberService;
exports.CommuneMemberService = CommuneMemberService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CommuneMemberService);
//# sourceMappingURL=commune-member.service.js.map