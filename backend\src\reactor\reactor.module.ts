import { Module } from "@nestjs/common";
import { ReactorService } from "./reactor.service";
import { ReactorController } from "./http/reactor.controller";
import { ReactorLensService } from "./lens/lens.service";
import { RatingModule } from "src/rating/rating.module";
import { MinioModule } from "src/minio/minio.module";

@Module({
    imports: [RatingModule, MinioModule],
    controllers: [ReactorController],
    providers: [ReactorService, ReactorLensService],
    exports: [ReactorService],
})
export class ReactorModule {}
