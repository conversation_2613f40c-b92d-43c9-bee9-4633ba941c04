// @ts-nocheck
import type { Common, User } from "@commune/api";
import type { PageLoad } from "./$types";

import { handleUnauthorized } from "$lib";

export interface User {
  id: string;
  email: string;
  role: User.UserRole;
  name: Common.Localizations;
  description: Common.Localizations;
  images?: {
    id: string;
    url: string;
    source: string;
  }[];
}

export const load = async ({ fetch, url }: Parameters<PageLoad>[0]) => {
  const response = await fetch("/api/user");

  handleUnauthorized(response, url);

  const data: User[] = response.ok ? await response.json() : [];

  return {
    users: data,
    isHasMoreUsers: data.length === 20, // If we got a full page, there might be more
  };
};
