import type { PageLoad } from "./$types";
import type { Auth, Reactor } from "@commune/api";

import { error } from "@sveltejs/kit";
import { handleUnauthorized } from "$lib";

async function fetchHub(fetch: typeof globalThis.fetch, hubId: string): Promise<Reactor.GetHubsOutput[0]> {
  const response = await fetch(
    `/api/reactor/hub?page=1&size=1&body=${encodeURIComponent(
      JSON.stringify({ ids: [hubId] }),
    )}`,
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch hub: ${response.statusText}`);
  }

  const hubs: Reactor.GetHubsOutput = await response.json();
  
  if (hubs.length === 0) {
    throw error(404, "Hub not found");
  }

  return hubs[0];
}

async function fetchCurrentUser(fetch: typeof globalThis.fetch): Promise<Auth.GetMeResponse | null> {
  try {
    const response = await fetch("/api/auth/me");
    return response.ok ? await response.json() : null;
  } catch {
    return null;
  }
}

const PAGE_SIZE = 20;

async function fetchInitialHubCommunities(fetch: typeof globalThis.fetch, hubId: string): Promise<Reactor.GetCommunitiesOutput> {
  try {
    const response = await fetch(
      `/api/reactor/community?page=1&size=${PAGE_SIZE}&body=${encodeURIComponent(
        JSON.stringify({ hubId }),
      )}`,
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch hub communities: ${response.statusText}`);
    }

    return response.json();
  } catch (error) {
    console.error("Error fetching initial hub communities:", error);
    return []; // Return empty array on error
  }
}



export const load: PageLoad = async ({ fetch, url, params }) => {
  const hubId = params.id;

  if (!hubId) {
    throw error(400, "Hub ID is required");
  }

  const [user, hub, initialCommunities] = await Promise.all([
    fetchCurrentUser(fetch),
    fetchHub(fetch, hubId).catch((err) => {
      if (err.status === 404) {
        throw err;
      }
      // For other errors, check if it's an auth issue
      const testResponse = fetch("/api/reactor/hub?page=1&size=1");
      testResponse.then((response) => {
        handleUnauthorized(response, url);
      });
      throw err;
    }),
    fetchInitialHubCommunities(fetch, hubId),
  ]);

  // Check if user can edit this hub (admin or head user)
  const canEdit = user && (user.role === "admin" || user.id === hub.headUser.id);

  return {
    hub,
    user,
    canEdit,
    initialCommunities,
    isHasMoreCommunities: initialCommunities.length === PAGE_SIZE,
    pageSize: PAGE_SIZE,
  };
};
