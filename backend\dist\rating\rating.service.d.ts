import type { Common } from "@commune/api";
import type { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { Prisma, UserRatingEntityType } from "@prisma/client";
export declare class RatingService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getUserSummary(userId: string): Promise<{
        rating: number;
        karma: number;
        rate: number | null;
    }>;
    getKarmaPoints(userId: string, pagination?: Common.Pagination): Promise<({
        sourceUser: {
            name: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
                value: string;
            }[];
            images: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
        };
        comment: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        targetUserId: string;
        quantity: number;
        sourceUserId: string;
    })[]>;
    spendKarmaPoint(data: {
        sourceUserId: string;
        targetUserId: string;
        quantity: number;
        comment: Common.Localizations;
    }, user: CurrentUser): Promise<{
        id: string;
    }>;
    getUserFeedbacks(userId: string, pagination?: Common.Pagination): Promise<({
        sourceUser: {
            name: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
                value: string;
            }[];
            images: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
        };
        text: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        value: number;
        targetUserId: string;
        sourceUserId: string;
        isAnonymous: boolean;
    })[]>;
    createUserFeedback(data: {
        sourceUserId: string;
        targetUserId: string;
        value: number;
        isAnonymous: boolean;
        text: Common.Localizations;
    }, user: CurrentUser): Promise<{
        id: string;
    }>;
    upsertRelativeUserRating(data: {
        sourceUserId: string;
        targetUserId: string;
        entityType: UserRatingEntityType;
        entityId: string;
        value: number;
    }, prisma?: Prisma.TransactionClient): Promise<void>;
    deleteRelativeUserRating(data: {
        sourceUserId: string;
        targetUserId: string;
        entityType: UserRatingEntityType;
        entityId: string;
    }, prisma?: Prisma.TransactionClient): Promise<void>;
}
