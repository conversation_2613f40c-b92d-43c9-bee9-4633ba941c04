"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoteService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const base_service_1 = require("../common/base-service");
const prisma_service_1 = require("../prisma/prisma.service");
const utils_1 = require("../utils");
let VoteService = class VoteService extends base_service_1.BaseService {
    constructor(prisma) {
        super("vote");
        this.prisma = prisma;
    }
    async canGet(id, user) {
        if (user.role !== client_1.UserRole.admin) {
            throw new common_1.ForbiddenException();
        }
        return true;
    }
    async check(ids) {
        return await this._check(ids, this.prisma.vote, "vote");
    }
    async getOne(id) {
        return await this.prisma.vote.findUnique({
            where: { id, deletedAt: null },
        });
    }
    async getOneOrThrow(id) {
        const vote = await this.getOne(id);
        if (!vote) {
            throw this.createNotFoundException();
        }
        return vote;
    }
    async getMany(where, pagination) {
        return await this.prisma.vote.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
        });
    }
    async createOne(data) {
        return await this.prisma.vote.create({ data });
    }
    async createMany(data) {
        return await this.prisma.vote.createMany({ data });
    }
    async create(data) {
        await this.prisma.$transaction(async (trx) => {
            await trx.vote.deleteMany({
                where: {
                    actorType: client_1.VoteActorType.user,
                    actorId: data.userId,
                    votingId: data.votingId,
                },
            });
            await trx.vote.create({
                data: {
                    actorType: client_1.VoteActorType.user,
                    actorId: data.userId,
                    votingId: data.votingId,
                    optionId: data.optionId,
                },
            });
        });
    }
    async updateOne(id, data) {
        return await this.prisma.vote.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }
    async updateMany(where, data) {
        return await this.prisma.vote.updateMany({ where, data });
    }
    async softDeleteOne(id) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }
    async softDeleteMany(where) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }
    async deleteOne(id) {
        return await this.prisma.vote.delete({ where: { id } });
    }
    async deleteMany(where) {
        return await this.prisma.vote.deleteMany({ where });
    }
};
exports.VoteService = VoteService;
exports.VoteService = VoteService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], VoteService);
//# sourceMappingURL=vote.service.js.map