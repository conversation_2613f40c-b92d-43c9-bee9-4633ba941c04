<script>
  import Header from "./layout/header.svelte";
  import Footer from "./layout/footer.svelte";

  const { children, data } = $props();
  const { locale, routeLocale, toLocaleHref } = $derived(data);
</script>

<div class="page-wrapper">
  <Header {locale} {toLocaleHref} {routeLocale} />

  <main class="container flex-grow-1 mb-5">
    {@render children()}
  </main>

  <Footer {locale} {toLocaleHref} />
</div>

<style>
  .page-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
</style>
