import {
    Body,
    Controller,
    Delete,
    FileTypeValidator,
    Get,
    HttpCode,
    HttpStatus,
    MaxFileSizeValidator,
    Param,
    ParseFilePipe,
    Post,
    Put,
    Query,
    UploadedFile,
    UseGuards,
    UseInterceptors,
} from "@nestjs/common";
import { ZodPipe } from "src/zod";
import { CurrentUser } from "src/auth/types";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { HttpSessionAuthGuard } from "src/auth/http/session-auth.guard";
import { ReactorService } from "../reactor.service";

import { Common, Reactor } from "@commune/api";
import {
    ALLOWED_FILE_TYPES,
    MAX_FILE_SIZE,
} from "src/commune/http/file-upload.dto";
import { FileInterceptor } from "@nestjs/platform-express";

@Controller("reactor")
@UseGuards(HttpSessionAuthGuard)
export class ReactorController {
    constructor(private readonly reactorService: ReactorService) {}

    @Post("post2")
    async createMockPost() {
        return await this.reactorService.createMockPost();
    }

    @Get("post2")
    async getPosts2() {
        const posts = await this.reactorService.getPosts2(
            null,
            {
                page: 1,
                size: 1000,
            },
            {
                id: "0",
                email: "<EMAIL>",
                role: "admin",
                isAdmin: true,
            },
        );

        return posts;
    }

    @Get("post")
    async getPosts(
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const response = await this.reactorService.getPosts(pagination, user);

        return Common.parseInput(Reactor.GetPostsResponseSchema, response);
    }

    @Get("post/:id")
    async getPost(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const post = await this.reactorService.getPost(id, user);

        return Common.parseInput(Reactor.PostSchema, post);
    }

    @Put("post")
    async createPost(
        @Body(new ZodPipe(Reactor.CreatePostRequestSchema))
        body: Reactor.CreatePostRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return await this.reactorService.createPost(body, user);
    }

    @Put("post/:id")
    async updatePost(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Reactor.UpdatePostRequestSchema))
        body: Reactor.UpdatePostRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return await this.reactorService.updatePost(id, body, user);
    }

    @Post("post/:id/rating")
    async updatePostRating(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Reactor.UpdatePostRatingRequestSchema))
        body: Reactor.UpdatePostRatingRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const newRating = await this.reactorService.updatePostRating(
            id,
            body,
            user,
        );

        return Common.parseInput(
            Reactor.UpdatePostRatingResponseSchema,
            newRating,
        );
    }

    @Post("post/:id/usefulness")
    async updatePostUsefulness(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Reactor.UpdatePostUsefulnessRequestSchema))
        body: Reactor.UpdatePostUsefulnessRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const newUsefulness = await this.reactorService.updatePostUsefulness(
            id,
            body,
            user,
        );

        return Common.parseInput(
            Reactor.UpdatePostUsefulnessResponseSchema,
            newUsefulness,
        );
    }

    @Delete("post/:id")
    async deletePost(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Reactor.DeletePostRequestSchema))
        body: Reactor.DeletePostRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return await this.reactorService.deletePost(id, body, user);
    }

    @Get("comment")
    async getComments(
        @Query(new ZodPipe(Reactor.GetCommentsRequestSchema))
        body: Reactor.GetCommentsRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const response = await this.reactorService.getComments(body, user);

        return Common.parseInput(Reactor.GetCommentsResponseSchema, response);
    }

    @Get("comment/:id")
    async getComment(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const comment = await this.reactorService.getComment({ id }, user);

        return Common.parseInput(Reactor.CommentSchema, comment);
    }

    @Put("comment")
    async createComment(
        @Body(new ZodPipe(Reactor.CreateCommentRequestSchema))
        body: Reactor.CreateCommentRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return await this.reactorService.createComment(body, user);
    }

    @Put("comment/:id")
    async updateComment(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Reactor.UpdateCommentRequestSchema))
        body: Reactor.UpdateCommentRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return await this.reactorService.updateComment(id, body, user);
    }

    @Put("comment/:id/rating")
    async updateCommentRating(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Reactor.UpdateCommentRatingRequestSchema))
        body: Reactor.UpdateCommentRatingRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const newRating = await this.reactorService.updateCommentRating(
            id,
            body,
            user,
        );

        return Common.parseInput(
            Reactor.UpdateCommentRatingResponseSchema,
            newRating,
        );
    }

    @Put("comment/:id/anonimify")
    async anonimifyComment(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Reactor.AnonimifyCommentRequestSchema))
        body: Reactor.AnonimifyCommentRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return await this.reactorService.anonimifyComment(id, body, user);
    }

    @Delete("comment/:id")
    async deleteComment(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Reactor.DeleteCommentRequestSchema))
        body: Reactor.DeleteCommentRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return await this.reactorService.deleteComment(id, body, user);
    }

    @Get("lens")
    async getLenses(@HttpCurrentUser() user: CurrentUser) {
        return await this.reactorService.getLenses(user);
    }

    @Put("lens")
    async createLens(
        @Body(new ZodPipe(Reactor.CreateLensRequestSchema))
        body: Reactor.CreateLensRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return await this.reactorService.createLens(body, user);
    }

    @Put("lens/:id")
    async updateLens(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Reactor.UpdateLensRequestSchema))
        body: Reactor.UpdateLensRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return await this.reactorService.updateLens(id, body, user);
    }

    @Delete("lens/:id")
    async deleteLens(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return await this.reactorService.deleteLens(id, user);
    }

    @Get("hub")
    async getHubs(
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
        @Query(
            "body",
            new ZodPipe(
                Common.JsonStringToObject(Reactor.GetHubsInputSchema.shape),
            ),
        )
        body: Reactor.GetHubsInput,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const hubs = await this.reactorService.getHubs(
            {
                input: body,
                pagination,
            },
            user,
        );

        return Common.parseInput(Reactor.GetHubsOutputSchema, hubs);
    }

    @Post("hub")
    async createHub(
        @Body(new ZodPipe(Reactor.CreateHubInputSchema))
        body: Reactor.CreateHubInput,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const hub = await this.reactorService.createHub(body, user);

        return Common.parseInput(Common.ObjectWithIdSchema, hub);
    }

    @Put("hub/:id")
    async updateHub(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Reactor.UpdateHubInputSchema))
        body: Reactor.UpdateHubInput,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.reactorService.updateHub(id, body, user);
    }

    @Put("hub/:id/image")
    @UseInterceptors(FileInterceptor("image"))
    async updateHubImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,

        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
                    new FileTypeValidator({
                        fileType: ALLOWED_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        file: Express.Multer.File,
    ) {
        await this.reactorService.updateHubImage(id, file, user);
    }

    @Delete("hub/:id/image")
    async deleteHubImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.reactorService.deleteHubImage(id, user);
    }

    @Get("community")
    @HttpCode(HttpStatus.OK)
    async getCommunities(
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
        @Query(
            "body",
            new ZodPipe(
                Common.JsonStringToObject(
                    Reactor.GetCommunitiesInputSchema.shape,
                ),
            ),
        )
        body: Reactor.GetCommunitiesInput,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const communities = await this.reactorService.getCommunities(
            {
                input: body,
                pagination,
            },
            user,
        );

        return Common.parseInput(
            Reactor.GetCommunitiesOutputSchema,
            communities,
        );
    }

    @Post("community")
    async createCommunity(
        @Body(new ZodPipe(Reactor.CreateCommunityInputSchema))
        body: Reactor.CreateCommunityInput,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const community = await this.reactorService.createCommunity(body, user);

        return Common.parseInput(Common.ObjectWithIdSchema, community);
    }

    @Put("community/:id")
    async updateCommunity(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @Body(new ZodPipe(Reactor.UpdateCommunityInputSchema))
        body: Reactor.UpdateCommunityInput,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.reactorService.updateCommunity(id, body, user);
    }

    @Put("community/:id/image")
    @UseInterceptors(FileInterceptor("image"))
    async updateCommunityImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,

        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
                    new FileTypeValidator({
                        fileType: ALLOWED_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        file: Express.Multer.File,
    ) {
        await this.reactorService.updateCommunityImage(id, file, user);
    }

    @Delete("community/:id/image")
    async deleteCommunityImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.reactorService.deleteCommunityImage(id, user);
    }
}
